<html>
  <head>
    <title>
    PDF
    </title>
    
     <link id="favicon" rel="icon" href="favicon.png"><link  rel="icon" href="favicon.png">
    <meta name="description" content="PropVR - Collabarative VR Plaform">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
		<meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    <meta  name="viewport" content="width=device-width,initial-scale=1.0">
    <link href="css/magflip.min.css" rel="stylesheet" type="text/css">
  <link href="css/themify-icons.min.css" rel="stylesheet" type="text/css">
    
     <script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
     <script src="js/magflip.min.js" type="text/javascript"></script>
    <style>
    .df-floating .df-ui-controls {
        pointer-events:all !important;
      }
      
            .df-container{
        background: rgba(0, 0, 0, 0.12) !important;
    height: 100% !important;
      }
      
      .hide_button{
        display:none !important;
      }
      
      .hide_pointer{
        pointer-events:none !important;
      }
      canvas{
        position:absolute;
        top:0;
        right:0;
        left:0;
        bottom:0;
        margin:auto;
        pointer-events: none;
      }

      .hide_button{
        display:none !important;
      }
      

    </style>
  </head>
  <body>
      <div id="df_manual_book">
	        </div>
    
    <script>
      
       var match;
      //   var pl = /\+/g;  // Regex for replacing addition symbol with a space
      //   var search = /([^&=]+)=?([^&]*)/g;
      // //  var decode = function (s) { return decodeURIComponent(s.replace(pl, ' ')); };
      //   var query = window.location.search.substring(1);
      //   var urlParams = {};
      //   match = search.exec(query);
      //   while (match) {
      //    // urlParams[decode(match[1])] = decode(match[2]);
      //     match = search.exec(query);
      //   }

      var url = window.location.search;
     
      url = url.split("?data=");
      url = url[1].split("&type=");
       var urlParams = url[0];
    
        if(urlParams!=''){
            
          var pdfFileURL=urlParams;
           // var pdfFileURL=urlParams.data+"&token="+urlParams.token;
           // pdfFileURL = encodeURI(pdfFileURL);
           // console.log(pdfFileURL);
           if(url[1]=="host"){
            var options = {hard:'cover', pageMode: DFLIP.PAGE_MODE.SINGLE,webgl:true,height: 500, duration: 800,hideControls: "share,thumbnail,download,more,pageMode,startPage,endPage,sound,pageNumber,play,outline"};

           }else{
            var options = {hard:'cover',pageMode: DFLIP.PAGE_MODE.SINGLE,webgl:true,height: 500, duration: 800,hideControls: "share,altPrev,pageNumber,altNext,play,outline,thumbnail,share,download,more,pageMode,startPage,endPage,sound",onReady: function (flipBook) {
       
       $(".df-ui-next").hide();
      $(".df-ui-prev").hide();
      
       $(".df-ui-next").addClass('hide_button');
      $(".df-ui-prev").addClass('hide_button');
      $('#df_manual_book').addClass('hide_pointer');
      
    },};
           }
       document.getElementById('df_manual_book').innerHTML='';
       flipBook = $("#df_manual_book").flipBook(pdfFileURL, options);
        }
      
    
    </script>
  </body>
</html>