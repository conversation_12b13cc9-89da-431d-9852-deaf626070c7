"use strict";var MOCKUP={VERSION:"1",REVISION:"01"};(function(e){e.__extends=function(e,t){for(var a in t)if(t.hasOwnProperty(a))e[a]=t[a];function n(){this.constructor=e}n.prototype=t.prototype;e.prototype=new n;e.__super=t.prototype;return e};e.MODE={PLUGIN:0,BUILDER:1};e.GEOMETRY_TYPE={PLANE:0,BOX:1,MODEL:2};e.MATERIAL_FACE={FRONT:4,BACK:5};e.MOUSE={LEFT:0,MIDDLE:1,RIGHT:2};e.Vector3=THREE.Vector3;e.Vector2=THREE.Vector2;e.defaults={anisotropy:8,maxTextureSize:2048,groundTexture:"https://cdn.glitch.com/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Fwhite.jpg?v=1590203659857",textureLoadFallback:"blank"};e.overrideAsTemplate=false;e.enableLog=false;e.log=function(){console.log.apply(console,arguments)};e.error=function(){console.error.apply(console,arguments)};e.autoSave=function(){};e.libType="THREE";e.textureLoader=new THREE.TextureLoader;e.paperDefaults={geometryType:e.GEOMETRY_TYPE.PLANE,width:210,height:297,depth:.2,segments:150,widthScale:1,heightScale:1,folds:1,angles:[],backImage:null,frontImage:null,frontBump:null,backBump:null,mipmap:false,shininess:15,bumpScale:.4,stiffness:.02,color:16777215,skipMaterials:false,repeat:100,defaultImage:[,,,,"blank","blank"]};e.Paper=function(t){e.__extends(a,t);function a(t,a){t=jQuery.extend({},e.paperDefaults,t);this.geometryType=t.geometryType;this.width=t.width;this.widthScale=t.widthScale;this.height=t.height;this.depth=t.depth;this.segments=t.segments;this.folds=t.folds;this.angles=t.angles;this._shininess=t.shininess;this._bumpScale=t.bumpScale;this.stiffness=t.stiffness;this.color=t.color;this.heightScale=t.heightScale;this.defaultImage=t.defaultImage;this.baseType="Paper";this.type="Paper";this.subType=t.subType;if(t.cloneParent!=null)this.cloneParent=t.cloneParent;this.createGeometry();this.updateGeometry();if(!t.skipMaterials){if(!(t.skipLoad==true&&t.cloneParent!=null)){if(this.frontImage!=null&&t.defaultImage!=null)this.frontImage(t.frontImage!=null?t.frontImage:t.defaultImage[e.MATERIAL_FACE.FRONT]);if(this.backImage!=null&&t.defaultImage!=null)this.backImage(t.backImage!=null?t.backImage:t.defaultImage[e.MATERIAL_FACE.BACK]);if(this.frontBump!=null)this.frontBump(t.frontBump);if(this.backBump!=null)this.backBump(t.backBump)}}if(this.bumpScale!=null)this.bumpScale(t.bumpScale);if(a!=null)if(a.add!=null)a.add(this)}var n=function(t,a,n,r){if(t){var s=t.image;t.naturalWidth=s.naturalWidth;t.naturalHeight=s.naturalHeight;t.needsUpdate=true}if(t!==null&&n=="map"){t.anisotropy=0;if(e.defaults.anisotropy>0)t.anisotropy=e.defaults.anisotropy;if(THREE.skipPowerOfTwo==true){t.minFilter=THREE.LinearFilter;t.magFilter=THREE.LinearFilter}t.name=(new Date).toTimeString();if(a.textureRepeat!=null){t.wrapS=THREE.RepeatWrapping;t.wrapT=THREE.RepeatWrapping;if(e.defaults.anisotropy>0)t.anisotropy=e.defaults.anisotropy;t.repeat.set(a._textureRepeat,a._textureRepeat);if(a instanceof e.Ground){a.material.bumpMap=t;a.material.bumpScale=a.bumpScale();a.material.needsUpdate=true}}}if(a.geometryType==e.GEOMETRY_TYPE.PLANE){i(a.material[n]);a.material[n]=t;if(n=="bumpMap")a.material.bumpScale=a.bumpScale()}else{i(a.material.materials[r][n]);a.material.materials[r][n]=t;if(n=="bumpMap")a.material.materials[r].bumpScale=a.bumpScale();a.material.materials[r].needsUpdate=true}e.autoSave()};var r=function(e,t){return null};var i=function(e){if(e){if(e.image){if(e.image.nodeName=="CANVAS"){if(e.image.remove)e.image.remove();delete e.image}}if(e.dispose){e.dispose()}e=null}};var s=function(t,a,n){if(t.geometryType==e.GEOMETRY_TYPE.PLANE){i(t.material[a])}else{i(t.material.materials[n][a])}};var o=function(e){if(!(e instanceof THREE.Object3D)||!(e.parent instanceof THREE.Object3D))return null;if(e.parent instanceof THREE.Scene)return e.parent;return o(e.parent)};e.loadImage=function(t,a,i,s,l){if(a==null){var c=t.geometryType==e.GEOMETRY_TYPE.PLANE?t.material[s]!==null?t.material[s].sourceFile:null:t.material.materials[i]==null?null:t.material.materials[i][s]?t.material.materials[i][s].sourceFile:null;return c==null?null:c.indexOf("data:image")>-1?null:c}else{if(e.enableLog==true)console.log(a);var u=null;if(a.nodeName=="CANVAS"||a.nodeName=="IMG"){u=new THREE.Texture(a);u.needsUpdate=true;n(u,t,s,i);if(l!=null)l(t,u);var h=o(t);if(h!=null)h.renderRequestPending=true}else{if(a!="blank"){u=a==null?null:e.textureLoader.load(a,function(e){e.sourceFile=a;n(e,t,s,i);if(l!=null)l(t,e);var r=o(t);if(r!=null)r.renderRequestPending=true},void 0,function(n){if(u.image==null){if(t.defaultImage!=null){var o=t.geometryType==e.GEOMETRY_TYPE.PLANE?t.defaultImage[e.MATERIAL_FACE.FRONT]:t.defaultImage[i];if(a!==o&&o!=null){e.loadImage(t,o,i,s)}else if(a!==e.defaults.textureLoadFallback||o==null){e.loadImage(t,e.defaults.textureLoadFallback,i,s)}}else if(a!==e.defaults.textureLoadFallback){e.loadImage(t,e.defaults.textureLoadFallback,i,s)}}r(n,a)});if(u)u.mapping=THREE.UVMapping}else{n(null,t,s,i)}}return 0}};a.prototype.loadImage=function(t,a,n,r){return e.loadImage(this,t,a,n,r)};a.prototype.frontImage=function(t,a){return this.loadImage(t,e.MATERIAL_FACE.FRONT,"map",a)};a.prototype.backImage=function(t,a){return this.loadImage(t,e.MATERIAL_FACE.BACK,"map",a)};a.prototype.frontBump=function(t,a){return this.loadImage(t,e.MATERIAL_FACE.FRONT,"bumpMap",a)};a.prototype.backBump=function(t,a){return this.loadImage(t,e.MATERIAL_FACE.BACK,"bumpMap",a)};a.prototype.bumpScale=function(t){if(t==null){return this._bumpScale}else{this._bumpScale=t;if(this.geometryType==e.GEOMETRY_TYPE.PLANE){this.material.bumpScale=t}else if(this.geometryType==e.GEOMETRY_TYPE.BOX){this.material.materials[e.MATERIAL_FACE.FRONT].bumpScale=t;this.material.materials[e.MATERIAL_FACE.BACK].bumpScale=t}}};a.prototype.shininess=function(t){if(t==null){return this._shininess}else{this._shininess=t;if(this.geometryType==e.GEOMETRY_TYPE.PLANE){this.material.shininess=t}else if(this.geometryType==e.GEOMETRY_TYPE.BOX){this.material.materials[e.MATERIAL_FACE.FRONT].shininess=t;this.material.materials[e.MATERIAL_FACE.BACK].shininess=t}}};a.prototype.bumpImage=function(t){return this.loadImage(t,e.MATERIAL_FACE.FRONT,"bumpMap")};a.prototype.createCopy=function(t){if(t==null){t={};e.getParameter(this,t)}t.type=this.type;var a=e.createObject(t);a.cloneParent=null;a.copy(this,null);return a};a.prototype.createClone=function(t){if(t==null){t={};e.getParameter(this,t)}t.type=this.type;var a=e.createObject(t);a.material=this.material;a.cloneParent=a.cloneParent!=null?a.cloneParent:this.uuid;a.copy(this,null);return a};a.prototype.createGeometry=function(){var a={color:this.color,shading:THREE.SmoothShading,shininess:this._shininess};var n=new THREE.MeshPhongMaterial(a);if(this.geometryType==e.GEOMETRY_TYPE.BOX){var r=[n,n,n,n,new THREE.MeshPhongMaterial(a),new THREE.MeshPhongMaterial(a)];t.call(this,new THREE.BoxGeometry(this.width,this.height,this.depth,this.segments*this.folds,1,1),new THREE.MeshFaceMaterial(r))}else if(this.geometryType==e.GEOMETRY_TYPE.PLANE){t.call(this,new THREE.PlaneBufferGeometry(this.width,this.height),n)}};a.prototype.updateGeometry=function(){};return a}(THREE.Mesh)})(MOCKUP||(MOCKUP={}));(function(e){e.selected=null;e.Stage=function(t){e.__extends(a,t);function a(a){a=a||{};var n=this;n.postRender=null;t.call(n);if(a.skipLoad!==true){n.canvas=a.canvas||document.createElement("canvas");n.canvas=jQuery(this.canvas);n.camera=new THREE.PerspectiveCamera(30,n.width/n.height,4,5e4);n.renderer=new THREE.WebGLRenderer({canvas:n.canvas[0],antialias:true,alpha:true});n.renderer.setPixelRatio(a.pixelRatio);n.renderer.setSize(n.width,n.height);n.renderer.setClearColor(16777215,0);var r=n.orbitControl=new THREE.OrbitControls(n.camera,n.renderer.domElement);r.maxPolarAngle=Math.PI;if(e.mode!==e.MODE.PLUGIN){n.renderer.shadowMap.enabled=true;n.ground=new e.Ground({color:15658734,height:n.camera.far,width:n.camera.far},n)}n.ambientLight=new THREE.AmbientLight(4473924);n.add(n.ambientLight);var i=n.spotLight=new THREE.DirectionalLight(16777215,.5);i.position.set(0,1,0);if(a.webglShadow!=false){i.castShadow=true;i.shadow=new THREE.LightShadow(new THREE.PerspectiveCamera(70,1,200,2e3));i.shadow.bias=-222e-6;i.shadow.mapSize.width=1024;i.shadow.mapSize.height=1024}i.intensity=.6;if(i.shadow){var s=n.spotLightHelper=new THREE.CameraHelper(i.shadow.camera);s.visible=false;n.add(i);n.add(s)}if(a.stats==true){var o=n.stats=new Stats;o.domElement.style.position="absolute";o.domElement.style.top="60px";n.canvas.parent().append(jQuery(o.domElement))}n.enableSoftShadow(false);n.animateCount=0;n.renderCount=0;n.camera.position.set(-300,300,300);n.camera.lookAt(new THREE.Vector3(0,0,0));n.orbitControl.center.set(0,0,0);n.orbitControl.update();n.selectiveRendering=false;n.renderRequestPending=false;l()}this.type="Stage";n.cancelRAF=function(){cancelAnimationFrame(l);l=null};function l(){if(l)requestAnimationFrame(l);if(n.selectiveRendering!=true||n.selectiveRendering==true&&n.renderRequestPending==true){n.render()}}}a.prototype.enableSoftShadow=function(e){if(this.spotLight.shadow==null)return;e=e!=null?e:false;var t=this.renderer.shadowMap.type;this.renderer.shadowMap.type=THREE.PCFShadowMap;var a=this.spotLight;if(a.shadow.map){if(t==this.renderer.shadowMap.type)return;a.shadow.map.dispose();a.shadow.map=null}var n=e?4096:1024;a.shadow.mapSize.width=n;a.shadow.mapSize.height=n;a.shadow.bias=e?-875e-8:-5e-5;this.clearMaterials();this.renderRequestPending=true};a.prototype.clearMaterials=function(){var e=this.children.length;for(var t=e-1;t>=0;t--){var a=this.children[t];if(a.baseType&&a.baseType=="Paper"){if(a.material){if(a.material.materials!=null){for(var n=0;n<a.material.materials.length;n++)a.material.materials[n].needsUpdate=true}else{a.material.needsUpdate=true}}}}};a.prototype.clearChild=function(){if(this.spotLight.shadow.map){this.spotLight.shadow.map.dispose();this.spotLight.shadow.map=null}this.spotLight.castShadow=false;this.clearMaterials();var t=this.children.length;for(var a=t-1;a>=0;a--){var n=this.children[a];if(n instanceof e.Bundle){for(var r=n.children.length-1;r>=0;r--){e.clearChild(n.children[r])}}e.clearChild(n);n=null}this.render()};a.prototype.resizeAuto=function(){this.resizeCanvas(this.canvas.parent().width(),this.canvas.parent().height())};a.prototype.resizeCanvas=function(e,t){this.renderer.setSize(e,t);this.camera.aspect=e/t;this.camera.updateProjectionMatrix();this.renderRequestPending=true;if(this.resizeCallback!=null)this.resizeCallback()};a.prototype.render=function(){this.animateCount++;this.renderer.render(this,this.camera);if(this.stats!=null)this.stats.update();this.renderRequestPending=false;if(this.renderCallback!=null)this.renderCallback()};a.prototype.toJSON=function(){var t={metadata:{version:4.3,type:"Object",generator:"ObjectExporter"}};var a={};var n=function(e){if(t.geometries==null){t.geometries=[]}if(a[e.uuid]==null){var n=e.toJSON();delete n.metadata;a[e.uuid]=n;t.geometries.push(n)}return e.uuid};var r={};var i=function(e){if(t.materials==null){t.materials=[]}if(r[e.uuid]==null){var a=e.toJSON();delete a.metadata;r[e.uuid]=a;t.materials.push(a)}return e.uuid};var s=function(t){var a={};a.uuid=t.uuid;a.type=t.type;if(t.name!=="")a.name=t.name;if(JSON.stringify(t.userData)!=="{}")a.userData=t.userData;if(t.visible!==true)a.visible=t.visible;e.getParameter(t,a);if(t instanceof THREE.PerspectiveCamera){a.fov=t.fov;a.aspect=t.aspect;a.near=t.near;a.far=t.far}else if(t instanceof THREE.OrthographicCamera){a.left=t.left;a.right=t.right;a.top=t.top;a.bottom=t.bottom;a.near=t.near;a.far=t.far}else if(t instanceof THREE.AmbientLight){a.color=t.color.getHex()}else if(t instanceof THREE.DirectionalLight){a.color=t.color.getHex();a.intensity=t.intensity}else if(t instanceof THREE.PointLight){a.color=t.color.getHex();a.intensity=t.intensity;a.distance=t.distance;a.decay=t.decay}else if(t instanceof THREE.SpotLight){a.radius=t.radius;a.color=t.color.getHex();a.intensity=t.intensity;a.distance=t.distance;a.angle=t.angle;a.exponent=t.exponent;a.decay=t.decay}else if(t instanceof THREE.Mesh||t instanceof THREE.Line||t instanceof THREE.PointCloud){if(t instanceof e.Iphone){}else{a.geometry=n(t.geometry);a.material=i(t.material);if(t instanceof THREE.Line)a.mode=t.mode}}else if(t instanceof THREE.Sprite){a.material=i(t.material)}a.matrix=t.matrix.toArray();if(t.children.length>0){a.children=[];for(var r=0;r<t.children.length;r++){var o=t.children[r];if(o instanceof THREE.TransformControls||o instanceof THREE.BoxHelper){}else{if(!(t instanceof e.Bundle))a.children.push(s(t.children[r]))}}}return a};t.object=s(this);return t};a.prototype.hasChild=function(){var e=this.children;var t=false;for(var a=0;a<this.children.length;a++){if(e[a].baseType=="Paper"&&e[a].type!=="Ground"){t=true;break}}return t};return a}(THREE.Scene);e.getParameter=function(t,a){if(t.subType!=null)a.subType=t.subType;if(t.height!=null)a.height=t.height;if(t.width!=null)a.width=t.width;if(t.depth!=null)a.depth=t.depth;if(t.radius!=null)a.radius=t.radius;if(t.segments!=null)a.segments=t.segments;if(t.folds!=null)a.folds=t.folds;if(t.angles!=null)a.angles=t.angles;if(t.shininess!=null)a.shininess=t.shininess();if(t.bumpScale!=null)a.bumpScale=t.bumpScale();if(t.stiffness!=null)a.stiffness=t.stiffness;if(t.heightScale!=null)a.heightScale=t.heightScale;if(t.widthScale!=null)a.widthScale=t.widthScale;if(t.frontImage!=null)a.frontImage=t.frontImage();if(t.backImage!=null)a.backImage=t.backImage();if(t.backBump!=null)a.backBump=t.backBump();if(t.frontBump!=null)a.frontBump=t.frontBump();if(t.cloneParent!=null)a.cloneParent=t.cloneParent;if(t.textureRepeat!=null)a.textureRepeat=t.textureRepeat();if(t.resolutionHeight!=null)a.resolutionHeight=t.resolutionHeight;if(t.resolutionWidth!=null)a.resolutionWidth=t.resolutionWidth;if(t.screenSize!=null)a.screenSize=t.screenSize;if(e.overrideAsTemplate==true){e.getDefaultImage(a);if(a.defaultImage!=null){if(t.frontImage!=null)a.frontImage=a.defaultImage[4];if(t.backImage!=null)a.backImage=a.defaultImage[5]}}};e.getDefaultImage=function(t){if(t.defaultImage==null&&e.presets[t.type]!=null&&e.presets[t.type].options!=null){t.defaultImage=e.presets[t.type].options.defaultImage;if(t.subType!=null&&e.presets[t.type].menu!=null&&e.presets[t.type].menu.subMenu!=null&&e.presets[t.type].menu.subMenu[t.subType]!=null&&e.presets[t.type].menu.subMenu[t.subType].options.defaultImage!=null){t.defaultImage=e.presets[t.type].menu.subMenu[t.subType].options.defaultImage}}};e.createObject=function(t){if(t.defaultImage==null&&e.presets!=null&&e.presets[t.type]!=null&&e.presets[t.type].options!=null){t.defaultImage=e.presets[t.type].options.defaultImage;if(t.subType!=null&&e.presets[t.type].menu!=null&&e.presets[t.type].menu.subMenu!=null&&e.presets[t.type].menu.subMenu[t.subType]!=null&&e.presets[t.type].menu.subMenu[t.subType].options.defaultImage!=null){t.defaultImage=e.presets[t.type].menu.subMenu[t.subType].options.defaultImage}}var a=new e[t.type](t);if(t.cloneParent!=null)a.cloneParent=t.cloneParent;return a}})(MOCKUP||(MOCKUP={}));(function(e){var t=function(t){e.__extends(a,t);function a(a,n){a=a||{};a.geometryType=e.GEOMETRY_TYPE.PLANE;t.call(this,a,n);this.type="PlanePaper"}a.prototype.backImage=null;a.prototype.backBump=null;return a}(e.Paper);e.PlanePaper=t;var a=function(t){e.__extends(a,t);function a(a,n){a=a||{};a.geometryType=e.GEOMETRY_TYPE.BOX;t.call(this,a,n);this.type="BoxPaper";this.castShadow=true;this.receiveShadow=true}a.prototype.updateGeometry=function(){};return a}(e.Paper);e.BoxPaper=a;var n=function(t){e.__extends(a,t);function a(e,a){e=e||{};t.call(this,e,a);var n=6;for(var r=0;r<n;r++){this.angles[r]=this.angles[r]!=null?this.angles[r]:0}this.updateAngle();this.type="FoldBoxPaper"}a.prototype.updateGeometry=function(){};a.prototype.updateAngle=function(t){var a=this;var n=performance.now();var r=a.width*(1-Math.sin(a.stiffness/2*(a.stiffness/2))/2)-a.width*a.stiffness/20;var i=a.height;var s=a.segments;var o=a.folds;var l=a.stiffness;var c=r/2;var u=i/2;var h=r/o;var f=h*l;var p=h;var m=[];var d=[];var E=[];var v=[];var g=[];var T=[];var y=0;var M=a.depth;var x=Math.PI/2;var R=a.angles[4]||0;var b=o==3?p/200:0;var P=0;var H=0,w=[];w.push(H);var I=false;function S(){if(a.folds>=1){g[0]=[];T[o-1]=[];y=a.angles[1]*Math.PI/180;R=a.angles[4]*Math.PI/180;var e=Math.sin(R)*p;g[0][0]=g[0][1]=a.folds==1?new THREE.Vector3(-p*Math.cos(y),0,Math.sin(y)*p):new THREE.Vector3(p-c-p*Math.cos(y),0,Math.sin(y)*p);P=(a.angles[1]-90)*Math.PI/180;y=P;T[o-1][2]=T[o-1][3]=new THREE.Vector3(g[0][0].x-Math.cos(y)*M,0,g[0][0].z+Math.sin(y)*M);if(a.folds==1){g[0][1]=new THREE.Vector3(-p/2*Math.cos(R),0,p/2*Math.sin(R));T[o-1][2]=new THREE.Vector3(g[0][1].x-Math.cos(P)*M,0,g[0][1].z+Math.sin(P)*M)}y=(45+a.angles[1]/2)*Math.PI/180;if(a.folds>1){if(a.folds==2){y=(45+a.angles[1]/4-a.angles[4]/2)*Math.PI/180}else y=(45+a.angles[1]/4)*Math.PI/180}g[0][2]=a.folds==1?new THREE.Vector3(-Math.cos(y)*f/2,0,Math.sin(y)*f):new THREE.Vector3(p-c-b-Math.cos(y)*f/2,0,Math.sin(y)*f);T[o-1][1]=a.folds==1?new THREE.Vector3(g[0][2].x-Math.cos(P)*M,0,g[0][2].z+Math.sin(P)*M):g[0][2];g[0][3]=a.folds==1?new THREE.Vector3(0,0,0):new THREE.Vector3(p-c-b,0,0);if(a.folds==2){y=(a.angles[1]/2-a.angles[4]/2-90)*Math.PI/180}else if(a.folds==1){y=(a.angles[1]-90)*Math.PI/180}else{y=(a.angles[1]/2-90)*Math.PI/180}T[o-1][0]=new THREE.Vector3(g[0][3].x-Math.cos(y)*M,0,g[0][3].z+Math.sin(y)*M)}if(a.folds>=2){g[1]=[];T[o-2]=[];g[1][0]=g[0][3];T[o-2][3]=T[o-1][0];y=(135+a.angles[1]*3/4)*Math.PI/180;g[1][1]=new THREE.Vector3(p-c-Math.cos(y)*f,0,Math.sin(y)*f);T[o-2][2]=g[1][1];y=0;if(a.folds>2){y=(45-a.angles[4]*3/4)*Math.PI/180;g[1][2]=new THREE.Vector3(p*2-c+b-Math.cos(y)*f,0,Math.sin(y)*f);T[o-2][1]=g[1][2];g[1][3]=new THREE.Vector3(p*2-c+b,0,0);y=(a.angles[4]/2-90)*Math.PI/180;T[o-2][0]=new THREE.Vector3(g[1][3].x+Math.cos(y)*M*1.25,0,g[1][3].z+Math.sin(y)*M*1.25)}else{y=(135-a.angles[4]/4+a.angles[1]/2)*Math.PI/180;g[1][1]=new THREE.Vector3(p-c-Math.cos(y)*f/2,0,Math.sin(y)*f);T[0][2]=g[1][1];y=(180-a.angles[4])*Math.PI/180;g[1][2]=g[1][3]=new THREE.Vector3(p-c-Math.cos(y)*p,0,Math.sin(y)*p);y=(a.angles[4]-90)*Math.PI/180;T[0][0]=T[0][1]=new THREE.Vector3(g[1][3].x+Math.cos(y)*M,0,g[1][3].z+Math.sin(y)*M)}}if(a.folds>2){g[2]=[];T[0]=[];g[2][0]=g[1][3];T[0][3]=T[1][0];y=(135-a.angles[4]*1/4)*Math.PI/180;g[2][1]=new THREE.Vector3(p*2-c-Math.cos(y)*0,0,Math.sin(y)*f);T[0][2]=g[2][1];y=(180-a.angles[4])*Math.PI/180;g[2][2]=g[2][3]=new THREE.Vector3(p*2-c-Math.cos(y)*p,0,Math.sin(y)*p);y=(a.angles[4]-90)*Math.PI/180;T[0][0]=T[0][1]=new THREE.Vector3(g[2][3].x+Math.cos(y)*M,0,g[2][3].z+Math.sin(y)*M)}for(var t=0;t<a.folds;t++){m[t]=new THREE.CubicBezierCurve3(g[t][0],g[t][1],g[t][2],g[t][3]);E[t]=m[t].getPoints(a.segments);var n,r=E[t][0];for(var i=1;i<E[t].length;i++){n=E[t][i];H+=n.distanceTo(r);w.push(H);r=n}d[t]=new THREE.CubicBezierCurve3(T[t][0],T[t][1],T[t][2],T[t][3]);v[t]=d[t].getPoints(a.segments)}}S();var z=H;var O=a.geometry;var A=o*s+1;var C=7,L=8;O.vertices[0].z=O.vertices[2].z=O.vertices[L+A*2-1].z=O.vertices[L+A*3-1].z=O.vertices[L+A*5-1].z=O.vertices[L+A*6-1].z=E[o-1][s].z;O.vertices[0].x=O.vertices[2].x=O.vertices[L+A*2-1].x=O.vertices[L+A*3-1].x=O.vertices[L+A*5-1].x=O.vertices[L+A*6-1].x=E[o-1][s].x;O.vertices[1].z=O.vertices[3].z=O.vertices[L+A-1].z=O.vertices[L+A*4-1].z=O.vertices[L+A*6].z=O.vertices[L+A*7].z=v[0][0].z;O.vertices[1].x=O.vertices[3].x=O.vertices[L+A-1].x=O.vertices[L+A*4-1].x=O.vertices[L+A*6].x=O.vertices[L+A*7].x=v[0][0].x;O.vertices[5].z=O.vertices[7].z=O.vertices[L+A].z=O.vertices[L+A*2].z=O.vertices[L+A*4].z=O.vertices[L+A*5].z=E[0][0].z;O.vertices[5].x=O.vertices[7].x=O.vertices[L+A].x=O.vertices[L+A*2].x=O.vertices[L+A*4].x=O.vertices[L+A*5].x=E[0][0].x;O.vertices[4].z=O.vertices[6].z=O.vertices[L].z=O.vertices[L+A*3].z=O.vertices[L+A*7-1].z=O.vertices[L+A*8-1].z=v[o-1][s].z;O.vertices[4].x=O.vertices[6].x=O.vertices[L].x=O.vertices[L+A*3].x=O.vertices[L+A*7-1].x=O.vertices[L+A*8-1].x=v[o-1][s].x;var N=O.vertices.length;L++;for(var V=0;V<o;V++){var k=V==0;var _=k?s-1:s;for(var B=0;B<_;B++){O.vertices[L].z=O.vertices[L+A*3].z=O.vertices[N-L+C-A].z=O.vertices[N-L+C].z=v[o-1-V][_-B].z;O.vertices[L].x=O.vertices[L+A*3].x=O.vertices[N-L+C-A].x=O.vertices[N-L+C].x=v[o-1-V][_-B].x;O.vertices[L+A].z=O.vertices[L+A*2].z=O.vertices[L+A*4].z=O.vertices[L+A*5].z=E[V][B+k].z;O.vertices[L+A].x=O.vertices[L+A*2].x=O.vertices[L+A*4].x=O.vertices[L+A*5].x=E[V][B+k].x;L++}}var j=O.faceVertexUvs[0];var D=O.faces;var F=0,U=0;for(var B=0;B<j.length;B++){if(D[B].materialIndex==e.MATERIAL_FACE.FRONT){var Y=w[U]/H;if(B%2==0){j[B][0].x=j[B][1].x=j[B+1][0].x=Y;U++}else{j[B-1][2].x=j[B][1].x=j[B][2].x=Y}}else if(D[B].materialIndex==e.MATERIAL_FACE.BACK){var Y=1-w[U]/H;if(B%2==0){j[B][0].x=j[B][1].x=j[B+1][0].x=Y;U--}else{j[B-1][2].x=j[B][1].x=j[B][2].x=Y}}}O.computeBoundingBox();var G=Math.abs(O.boundingBox.min.x-O.boundingBox.max.x);var W=Math.abs(O.boundingBox.min.z-O.boundingBox.max.z);var K=Math.sqrt(G*G+W*W);a.scale.x=a.scale.z=p*a.folds/H;O.computeBoundingSphere();O.verticesNeedUpdate=true;O.computeFaceNormals();O.computeVertexNormals();O.uvsNeedUpdate=true;O.normalsNeedUpdate=true;m.forEach(function(e){e=null});d.forEach(function(e){e=null});v.forEach(function(e){e=null});E.forEach(function(e){e=null})};return a}(e.BoxPaper);e.FlexBoxPaper=n;var n=function(t){e.__extends(a,t);function a(e,a){e=e||{};t.call(this,e,a);var n=6;for(var r=0;r<n;r++){this.angles[r]=this.angles[r]!=null?this.angles[r]:0}this.updateAngle();this.type="FoldBoxPaper"}a.prototype.updateGeometry=function(){};a.prototype.updateAngle=function(t){var a=this;var n=performance.now();var r=a.width*(1-Math.sin(a.stiffness/2*(a.stiffness/2))/2)-a.width*a.stiffness/20;var i=a.height;var s=a.segments;var o=a.folds;var l=a.stiffness;var c=r/2;var u=i/2;var h=r/o;var f=h*l;var p=h;var m=[];var d=[];var E=[];var v=[];var g=[];var T=[];var y=0;var M=a.depth;var x=Math.PI/2;var R=a.angles[4]||0;var b=o==3?p/200:0;var P=0;var H=0,w=[];w.push(H);var I=false;function S(){if(a.folds>=1){g[0]=[];T[o-1]=[];y=a.angles[1]*Math.PI/180;R=a.angles[4]*Math.PI/180;var e=Math.sin(R)*p;g[0][0]=g[0][1]=a.folds==1?new THREE.Vector3(-p*Math.cos(y),0,Math.sin(y)*p):new THREE.Vector3(p-c-p*Math.cos(y),0,Math.sin(y)*p);P=(a.angles[1]-90)*Math.PI/180;y=P;T[o-1][2]=T[o-1][3]=new THREE.Vector3(g[0][0].x-Math.cos(y)*M,0,g[0][0].z+Math.sin(y)*M);if(a.folds==1){g[0][1]=new THREE.Vector3(-p/2*Math.cos(R),0,p/2*Math.sin(R));T[o-1][2]=new THREE.Vector3(g[0][1].x-Math.cos(P)*M,0,g[0][1].z+Math.sin(P)*M)}y=(45+a.angles[1]/2)*Math.PI/180;if(a.folds>1){if(a.folds==2){y=(45+a.angles[1]/4-a.angles[4]/2)*Math.PI/180}else y=(45+a.angles[1]/4)*Math.PI/180}g[0][2]=a.folds==1?new THREE.Vector3(-Math.cos(y)*f/2,0,Math.sin(y)*f):new THREE.Vector3(p-c-b-Math.cos(y)*f/2,0,Math.sin(y)*f);T[o-1][1]=a.folds==1?new THREE.Vector3(g[0][2].x-Math.cos(P)*M,0,g[0][2].z+Math.sin(P)*M):g[0][2];g[0][3]=a.folds==1?new THREE.Vector3(0,0,0):new THREE.Vector3(p-c-b,0,0);if(a.folds==2){y=(a.angles[1]/2-a.angles[4]/2-90)*Math.PI/180}else if(a.folds==1){y=(a.angles[1]-90)*Math.PI/180}else{y=(a.angles[1]/2-90)*Math.PI/180}T[o-1][0]=new THREE.Vector3(g[0][3].x-Math.cos(y)*M,0,g[0][3].z+Math.sin(y)*M)}if(a.folds>=2){g[1]=[];T[o-2]=[];g[1][0]=g[0][3];T[o-2][3]=T[o-1][0];y=(135+a.angles[1]*3/4)*Math.PI/180;g[1][1]=new THREE.Vector3(p-c-Math.cos(y)*f,0,Math.sin(y)*f);T[o-2][2]=g[1][1];y=0;if(a.folds>2){y=(45-a.angles[4]*3/4)*Math.PI/180;g[1][2]=new THREE.Vector3(p*2-c+b-Math.cos(y)*f,0,Math.sin(y)*f);T[o-2][1]=g[1][2];g[1][3]=new THREE.Vector3(p*2-c+b,0,0);y=(a.angles[4]/2-90)*Math.PI/180;T[o-2][0]=new THREE.Vector3(g[1][3].x+Math.cos(y)*M*1.25,0,g[1][3].z+Math.sin(y)*M*1.25)}else{y=(135-a.angles[4]/4+a.angles[1]/2)*Math.PI/180;g[1][1]=new THREE.Vector3(p-c-Math.cos(y)*f/2,0,Math.sin(y)*f);T[0][2]=g[1][1];y=(180-a.angles[4])*Math.PI/180;g[1][2]=g[1][3]=new THREE.Vector3(p-c-Math.cos(y)*p,0,Math.sin(y)*p);y=(a.angles[4]-90)*Math.PI/180;T[0][0]=T[0][1]=new THREE.Vector3(g[1][3].x+Math.cos(y)*M,0,g[1][3].z+Math.sin(y)*M)}}if(a.folds>2){g[2]=[];T[0]=[];g[2][0]=g[1][3];T[0][3]=T[1][0];y=(135-a.angles[4]*1/4)*Math.PI/180;g[2][1]=new THREE.Vector3(p*2-c-Math.cos(y)*0,0,Math.sin(y)*f);T[0][2]=g[2][1];y=(180-a.angles[4])*Math.PI/180;g[2][2]=g[2][3]=new THREE.Vector3(p*2-c-Math.cos(y)*p,0,Math.sin(y)*p);y=(a.angles[4]-90)*Math.PI/180;T[0][0]=T[0][1]=new THREE.Vector3(g[2][3].x+Math.cos(y)*M,0,g[2][3].z+Math.sin(y)*M)}for(var t=0;t<a.folds;t++){m[t]=new THREE.CubicBezierCurve3(g[t][0],g[t][1],g[t][2],g[t][3]);E[t]=m[t].getPoints(a.segments);var n,r=E[t][0];for(var i=1;i<E[t].length;i++){n=E[t][i];H+=n.distanceTo(r);w.push(H);r=n}d[t]=new THREE.CubicBezierCurve3(T[t][0],T[t][1],T[t][2],T[t][3]);v[t]=d[t].getPoints(a.segments)}}S();var z=H;var O=a.geometry;var A=o*s+1;var C=7,L=8;O.vertices[0].z=O.vertices[2].z=O.vertices[L+A*2-1].z=O.vertices[L+A*3-1].z=O.vertices[L+A*5-1].z=O.vertices[L+A*6-1].z=E[o-1][s].z;O.vertices[0].x=O.vertices[2].x=O.vertices[L+A*2-1].x=O.vertices[L+A*3-1].x=O.vertices[L+A*5-1].x=O.vertices[L+A*6-1].x=E[o-1][s].x;O.vertices[1].z=O.vertices[3].z=O.vertices[L+A-1].z=O.vertices[L+A*4-1].z=O.vertices[L+A*6].z=O.vertices[L+A*7].z=v[0][0].z;O.vertices[1].x=O.vertices[3].x=O.vertices[L+A-1].x=O.vertices[L+A*4-1].x=O.vertices[L+A*6].x=O.vertices[L+A*7].x=v[0][0].x;O.vertices[5].z=O.vertices[7].z=O.vertices[L+A].z=O.vertices[L+A*2].z=O.vertices[L+A*4].z=O.vertices[L+A*5].z=E[0][0].z;O.vertices[5].x=O.vertices[7].x=O.vertices[L+A].x=O.vertices[L+A*2].x=O.vertices[L+A*4].x=O.vertices[L+A*5].x=E[0][0].x;O.vertices[4].z=O.vertices[6].z=O.vertices[L].z=O.vertices[L+A*3].z=O.vertices[L+A*7-1].z=O.vertices[L+A*8-1].z=v[o-1][s].z;O.vertices[4].x=O.vertices[6].x=O.vertices[L].x=O.vertices[L+A*3].x=O.vertices[L+A*7-1].x=O.vertices[L+A*8-1].x=v[o-1][s].x;var N=O.vertices.length;L++;for(var V=0;V<o;V++){var k=V==0;var _=k?s-1:s;for(var B=0;B<_;B++){O.vertices[L].z=O.vertices[L+A*3].z=O.vertices[N-L+C-A].z=O.vertices[N-L+C].z=v[o-1-V][_-B].z;O.vertices[L].x=O.vertices[L+A*3].x=O.vertices[N-L+C-A].x=O.vertices[N-L+C].x=v[o-1-V][_-B].x;O.vertices[L+A].z=O.vertices[L+A*2].z=O.vertices[L+A*4].z=O.vertices[L+A*5].z=E[V][B+k].z;O.vertices[L+A].x=O.vertices[L+A*2].x=O.vertices[L+A*4].x=O.vertices[L+A*5].x=E[V][B+k].x;L++}}var j=O.faceVertexUvs[0];var D=O.faces;var F=0,U=0;for(var B=0;B<j.length;B++){if(D[B].materialIndex==e.MATERIAL_FACE.FRONT){var Y=w[U]/H;if(B%2==0){j[B][0].x=j[B][1].x=j[B+1][0].x=Y;U++}else{j[B-1][2].x=j[B][1].x=j[B][2].x=Y}}else if(D[B].materialIndex==e.MATERIAL_FACE.BACK){var Y=1-w[U]/H;if(B%2==0){j[B][0].x=j[B][1].x=j[B+1][0].x=Y;U--}else{j[B-1][2].x=j[B][1].x=j[B][2].x=Y}}}O.computeBoundingBox();var G=Math.abs(O.boundingBox.min.x-O.boundingBox.max.x);var W=Math.abs(O.boundingBox.min.z-O.boundingBox.max.z);var K=Math.sqrt(G*G+W*W);a.scale.x=a.scale.z=p*a.folds/H;O.computeBoundingSphere();O.verticesNeedUpdate=true;O.computeFaceNormals();O.computeVertexNormals();O.uvsNeedUpdate=true;O.normalsNeedUpdate=true;m.forEach(function(e){e=null});d.forEach(function(e){e=null});v.forEach(function(e){e=null});E.forEach(function(e){e=null})};return a}(e.BoxPaper);e.FlexBoxPaper=n;var r=function(t){e.__extends(a,t);function a(e,a){e=e||{};t.call(this);this.type="Bundle";if(a!=null)a.add(this)}return a}(THREE.Group);e.Bundle=r})(MOCKUP||(MOCKUP={}));(function(e){var t=function(t){e.__extends(a,t);function a(a,n){a=a||{};var r=this;r._textureRepeat=a.textureRepeat==null?50:a.textureRepeat;a.skipMaterials=true;a.bumpScale=a.bumpScale==null?.1:a.bumpScale;a.shininess=a.shininess==null?0:a.shininess;t.call(this,a,n);this.receiveShadow=true;this.angles=null;this.frontImage(a.frontImage==null?e.defaults.groundTexture:a.frontImage);r.type="Ground"}a.prototype.frontImage=function(e){if(e==null)return a.__super.frontImage.call(this,e);else{a.__super.frontImage.call(this,e)}};e.PlanePaper.prototype.frontBump=null;a.prototype.textureRepeat=function(e){if(e==null){return this._textureRepeat}else{this._textureRepeat=e;if(this.material.map!==null)this.material.map.repeat.set(e,e)}};return a}(e.PlanePaper);e.Ground=t})(MOCKUP||(MOCKUP={}));MOCKUP.inchTomm=function(e){return e*25.4};MOCKUP.mmToInch=function(e){return e/25.4};MOCKUP.clearChild=function(e){var t=e.material;e.parent.remove(e);var a;if(e.dispose!=null)e.dispose();if(e.geometry!=null)e.geometry.dispose();if(t==null)return;if(t.materials==null){if(t.map){a=t.map;t.dispose();a.dispose()}if(t.bumpMap){a=t.bumpMap;t.dispose();a.dispose()}}else{for(var n=0;n<t.materials.length;n++){if(t.materials[n]){if(t.materials[n].map){a=t.materials[n].map;t.materials[n].dispose();a.dispose()}if(t.materials[n].bumpMap){a=t.materials[n].bumpMap;t.materials[n].dispose();a.dispose()}}t.materials[n]=null}}t=null;a=null};THREE.OrbitControls=function(e,t){this.object=e;this.domElement=t!=null?t:document;this.enabled=true;this.target=new THREE.Vector3;this.center=this.target;this.noZoom=false;this.zoomSpeed=1;this.minDistance=0;this.maxDistance=Infinity;this.minZoom=0;this.maxZoom=Infinity;this.noRotate=false;this.rotateSpeed=1;this.noPan=false;this.keyPanSpeed=7;this.autoRotate=false;this.autoRotateSpeed=2;this.minPolarAngle=0;this.maxPolarAngle=Math.PI;this.minAzimuthAngle=-Infinity;this.maxAzimuthAngle=Infinity;this.noKeys=false;this.keys={LEFT:37,UP:38,RIGHT:39,BOTTOM:40};this.mouseButtons={ORBIT:THREE.MOUSE.LEFT,ZOOM:THREE.MOUSE.MIDDLE,PAN:THREE.MOUSE.RIGHT};var a=this;var n=1e-6;var r=new THREE.Vector2;var i=new THREE.Vector2;var s=new THREE.Vector2;var o=new THREE.Vector2;var l=new THREE.Vector2;var c=new THREE.Vector2;var u=new THREE.Vector3;var h=new THREE.Vector3;var f=new THREE.Vector2;var p=new THREE.Vector2;var m=new THREE.Vector2;var d;var E;var v=0;var g=0;var T=1;var y=new THREE.Vector3;var M=new THREE.Vector3;var x=new THREE.Quaternion;var R={NONE:-1,ROTATE:0,DOLLY:1,PAN:2,TOUCH_ROTATE:3,TOUCH_DOLLY:4,TOUCH_PAN:5};var b=R.NONE;this.target0=this.target.clone();this.position0=this.object.position.clone();this.zoom0=this.object.zoom;var P=(new THREE.Quaternion).setFromUnitVectors(e.up,new THREE.Vector3(0,1,0));var H=P.clone().inverse();var w={type:"change"};var I={type:"start"};var S={type:"end"};this.rotateLeft=function(e){if(e==null){e=z()}g-=e};this.rotateUp=function(e){if(e==null){e=z()}v-=e};this.panLeft=function(e){var t=this.object.matrix.elements;u.set(t[0],t[1],t[2]);u.multiplyScalar(-e);y.add(u)};this.panUp=function(e){var t=this.object.matrix.elements;u.set(t[4],t[5],t[6]);u.multiplyScalar(e);y.add(u)};this.pan=function(e,t){var n=a.domElement===document?a.domElement.body:a.domElement;if(a.object instanceof THREE.PerspectiveCamera){var r=a.object.position;var i=r.clone().sub(a.target);var s=i.length();s*=Math.tan(a.object.fov/2*Math.PI/180);a.panLeft(2*e*s/n.clientHeight);a.panUp(2*t*s/n.clientHeight)}else if(a.object instanceof THREE.OrthographicCamera){a.panLeft(e*(a.object.right-a.object.left)/n.clientWidth);a.panUp(t*(a.object.top-a.object.bottom)/n.clientHeight)}else{console.warn("WARNING: OrbitControls.js encountered an unknown camera type - pan disabled.")}};this.dollyIn=function(e){if(e==null){e=O()}if(a.object instanceof THREE.PerspectiveCamera){T/=e}else if(a.object instanceof THREE.OrthographicCamera){a.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom*e));a.object.updateProjectionMatrix();a.dispatchEvent(w)}else{console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.")}};this.dollyOut=function(e){if(e==null){e=O()}if(a.object instanceof THREE.PerspectiveCamera){T*=e}else if(a.object instanceof THREE.OrthographicCamera){a.object.zoom=Math.max(this.minZoom,Math.min(this.maxZoom,this.object.zoom/e));a.object.updateProjectionMatrix();a.dispatchEvent(w)}else{console.warn("WARNING: OrbitControls.js encountered an unknown camera type - dolly/zoom disabled.")}};this.update=function(){var e=this.object.position;h.copy(e).sub(this.target);h.applyQuaternion(P);d=Math.atan2(h.x,h.z);E=Math.atan2(Math.sqrt(h.x*h.x+h.z*h.z),h.y);if(this.autoRotate&&b===R.NONE){this.rotateLeft(z())}d+=g;E+=v;d=Math.max(this.minAzimuthAngle,Math.min(this.maxAzimuthAngle,d));E=Math.max(this.minPolarAngle,Math.min(this.maxPolarAngle,E));E=Math.max(n,Math.min(Math.PI-n,E));var t=h.length()*T;t=Math.max(this.minDistance,Math.min(this.maxDistance,t));this.target.add(y);h.x=t*Math.sin(E)*Math.sin(d);h.y=t*Math.cos(E);h.z=t*Math.sin(E)*Math.cos(d);h.applyQuaternion(H);e.copy(this.target).add(h);this.object.lookAt(this.target);g=0;v=0;T=1;y.set(0,0,0);if(M.distanceToSquared(this.object.position)>n||8*(1-x.dot(this.object.quaternion))>n){this.dispatchEvent(w);M.copy(this.object.position);x.copy(this.object.quaternion)}};this.reset=function(){b=R.NONE;this.target.copy(this.target0);this.object.position.copy(this.position0);this.object.zoom=this.zoom0;this.object.updateProjectionMatrix();this.dispatchEvent(w);this.update()};this.getPolarAngle=function(){return E};this.getAzimuthalAngle=function(){return d};function z(){return 2*Math.PI/60/60*a.autoRotateSpeed}function O(){return Math.pow(.95,a.zoomSpeed)}function A(e){if(a.enabled===false)return;e.preventDefault();if(e.button===a.mouseButtons.ORBIT){if(a.noRotate===true)return;b=R.ROTATE;r.set(e.clientX,e.clientY)}else if(e.button===a.mouseButtons.ZOOM){if(a.noZoom===true)return;b=R.DOLLY;f.set(e.clientX,e.clientY)}else if(e.button===a.mouseButtons.PAN){if(a.noPan===true)return;b=R.PAN;o.set(e.clientX,e.clientY)}if(b!==R.NONE){document.addEventListener("mousemove",C,false);document.addEventListener("mouseup",L,false);document.addEventListener("mouseout",L,false);a.dispatchEvent(I)}}function C(e){if(a.enabled===false)return;e.preventDefault();var t=a.domElement===document?a.domElement.body:a.domElement;if(b===R.ROTATE){if(a.noRotate===true)return;i.set(e.clientX,e.clientY);s.subVectors(i,r);a.rotateLeft(2*Math.PI*s.x/t.clientWidth*a.rotateSpeed);a.rotateUp(2*Math.PI*s.y/t.clientHeight*a.rotateSpeed);r.copy(i)}else if(b===R.DOLLY){if(a.noZoom===true)return;p.set(e.clientX,e.clientY);m.subVectors(p,f);if(m.y>0){a.dollyIn()}else if(m.y<0){a.dollyOut()}f.copy(p)}else if(b===R.PAN){if(a.noPan===true)return;l.set(e.clientX,e.clientY);c.subVectors(l,o);a.pan(c.x,c.y);o.copy(l)}if(b!==R.NONE)a.update()}function L(){if(a.enabled===false)return;document.removeEventListener("mousemove",C,false);document.removeEventListener("mouseup",L,false);document.removeEventListener("mouseout",L,false);a.dispatchEvent(S);b=R.NONE}function N(e){if(a.enabled===false||a.noZoom===true||b!==R.NONE)return;e.preventDefault();e.stopPropagation();var t=0;if(e.wheelDelta!=null){t=e.wheelDelta}else if(e.detail!=null){t=-e.detail}if(t>0){a.dollyOut()}else if(t<0){a.dollyIn()}a.update();a.dispatchEvent(I);a.dispatchEvent(S);if(a.zoomCallback)a.zoomCallback()}function V(e){if(a.enabled===false||a.noKeys===true||a.noPan===true)return;switch(e.keyCode){case a.keys.UP:a.pan(0,a.keyPanSpeed);a.update();break;case a.keys.BOTTOM:a.pan(0,-a.keyPanSpeed);a.update();break;case a.keys.LEFT:a.pan(a.keyPanSpeed,0);a.update();break;case a.keys.RIGHT:a.pan(-a.keyPanSpeed,0);a.update();break}}function k(e){if(a.enabled===false)return;var t=e.touches.length;if(a.mouseButtons.PAN==THREE.MOUSE.LEFT)t=3;switch(t){case 4:if(a.noRotate===true)return;b=R.TOUCH_ROTATE;r.set(e.touches[0].pageX,e.touches[0].pageY);break;case 2:if(a.noZoom===true)return;b=R.TOUCH_DOLLY;var n=e.touches[0].pageX-e.touches[1].pageX;var i=e.touches[0].pageY-e.touches[1].pageY;var s=Math.sqrt(n*n+i*i);f.set(0,s);break;case 3:if(a.noPan===true)return;b=R.TOUCH_PAN;o.set(e.touches[0].pageX,e.touches[0].pageY);break;default:b=R.NONE}if(b!==R.NONE)a.dispatchEvent(I)}function _(e){if(a.enabled===false)return;var t=a.domElement===document?a.domElement.body:a.domElement;var n=e.touches.length;if(a.mouseButtons.PAN==THREE.MOUSE.LEFT)n=3;switch(n){case 4:if(a.noRotate===true)return;if(b!==R.TOUCH_ROTATE)return;e.preventDefault();e.stopPropagation();i.set(e.touches[0].pageX,e.touches[0].pageY);s.subVectors(i,r);a.rotateLeft(2*Math.PI*s.x/t.clientWidth*a.rotateSpeed);a.rotateUp(2*Math.PI*s.y/t.clientHeight*a.rotateSpeed);r.copy(i);a.update();break;case 2:if(a.noZoom===true)return;if(b!==R.TOUCH_DOLLY)return;e.preventDefault();e.stopPropagation();var u=e.touches[0].pageX-e.touches[1].pageX;var h=e.touches[0].pageY-e.touches[1].pageY;var d=Math.sqrt(u*u+h*h);p.set(0,d);m.subVectors(p,f);if(m.y>0){a.dollyOut()}else if(m.y<0){a.dollyIn()}f.copy(p);a.update();break;case 3:if(a.noPan===true)return;if(b!==R.TOUCH_PAN)return;e.preventDefault();e.stopPropagation();l.set(e.touches[0].pageX,e.touches[0].pageY);c.subVectors(l,o);a.pan(c.x,c.y);o.copy(l);a.update();break;default:b=R.NONE}}function B(){if(a.enabled===false)return;a.dispatchEvent(S);b=R.NONE}function j(e){e.preventDefault()}this.dispose=function(){this.domElement.removeEventListener("contextmenu",j,false);this.domElement.removeEventListener("mousedown",A,false);this.domElement.removeEventListener("mousewheel",N,false);this.domElement.removeEventListener("DOMMouseScroll",N,false);this.domElement.removeEventListener("touchstart",k,false);this.domElement.removeEventListener("touchend",B,false);this.domElement.removeEventListener("touchmove",_,false);window.removeEventListener("keydown",V,false)};this.domElement.addEventListener("contextmenu",j,false);this.domElement.addEventListener("mousedown",A,false);this.domElement.addEventListener("mousewheel",N,false);this.domElement.addEventListener("DOMMouseScroll",N,false);this.domElement.addEventListener("touchstart",k,false);this.domElement.addEventListener("touchend",B,false);this.domElement.addEventListener("touchmove",_,false);window.addEventListener("keydown",V,false);this.update()};THREE.OrbitControls.prototype=Object.create(THREE.EventDispatcher.prototype);THREE.OrbitControls.prototype.constructor=THREE.OrbitControls;THREE.CSS3DObject=function(e){THREE.Object3D.call(this);this.element=e;this.element.style.position="absolute";this.addEventListener("removed",function(e){if(this.element.parentNode!==null){this.element.parentNode.removeChild(this.element)}})};THREE.CSS3DObject.prototype=Object.create(THREE.Object3D.prototype);THREE.CSS3DObject.prototype.constructor=THREE.CSS3DObject;THREE.CSS3DSprite=function(e){THREE.CSS3DObject.call(this,e)};THREE.CSS3DSprite.prototype=Object.create(THREE.CSS3DObject.prototype);THREE.CSS3DSprite.prototype.constructor=THREE.CSS3DSprite;THREE.CSS3DRenderer=function(){console.log("THREE.CSS3DRenderer",THREE.REVISION);var e,t;var a,n;var r=new THREE.Matrix4;var i={camera:{fov:0,style:""},objects:{}};var s=document.createElement("div");s.style.overflow="hidden";s.style.WebkitTransformStyle="preserve-3d";s.style.MozTransformStyle="preserve-3d";s.style.oTransformStyle="preserve-3d";s.style.transformStyle="preserve-3d";this.domElement=s;var o=document.createElement("div");o.style.WebkitTransformStyle="preserve-3d";o.style.MozTransformStyle="preserve-3d";o.style.oTransformStyle="preserve-3d";o.style.transformStyle="preserve-3d";s.appendChild(o);this.setClearColor=function(){};this.getSize=function(){return{width:e,height:t}};this.setSize=function(r,i){e=r;t=i;a=e/2;n=t/2;s.style.width=r+"px";s.style.height=i+"px";o.style.width=r+"px";o.style.height=i+"px"};var l=function(e){return Math.abs(e)<Number.EPSILON?0:e};var c=function(e){var t=e.elements;return"matrix3d("+l(t[0])+","+l(-t[1])+","+l(t[2])+","+l(t[3])+","+l(t[4])+","+l(-t[5])+","+l(t[6])+","+l(t[7])+","+l(t[8])+","+l(-t[9])+","+l(t[10])+","+l(t[11])+","+l(t[12])+","+l(-t[13])+","+l(t[14])+","+l(t[15])+")"};var u=function(e){var t=e.elements;return"translate3d(-50%,-50%,0) matrix3d("+l(t[0])+","+l(t[1])+","+l(t[2])+","+l(t[3])+","+l(-t[4])+","+l(-t[5])+","+l(-t[6])+","+l(-t[7])+","+l(t[8])+","+l(t[9])+","+l(t[10])+","+l(t[11])+","+l(t[12])+","+l(t[13])+","+l(t[14])+","+l(t[15])+")"};var h=function(e,t){if(e instanceof THREE.CSS3DObject){var a;if(e instanceof THREE.CSS3DSprite){r.copy(t.matrixWorldInverse);r.transpose();r.copyPosition(e.matrixWorld);r.scale(e.scale);r.elements[3]=0;r.elements[7]=0;r.elements[11]=0;r.elements[15]=1;a=u(r)}else{a=u(e.matrixWorld)}var n=e.element;var s=i.objects[e.id];if(s===undefined||s!==a){n.style.WebkitTransform=a;n.style.MozTransform=a;n.style.oTransform=a;n.style.transform=a;i.objects[e.id]=a}if(n.parentNode!==o){o.appendChild(n)}}for(var l=0,c=e.children.length;l<c;l++){h(e.children[l],t)}};this.render=function(e,r){var l=.5/Math.tan(THREE.Math.degToRad(r.fov*.5))*t;if(i.camera.fov!==l){s.style.WebkitPerspective=l+"px";s.style.MozPerspective=l+"px";s.style.oPerspective=l+"px";s.style.perspective=l+"px";i.camera.fov=l}e.updateMatrixWorld();if(r.parent===null)r.updateMatrixWorld();r.matrixWorldInverse.getInverse(r.matrixWorld);var u="translate3d(0,0,"+l+"px)"+c(r.matrixWorldInverse)+" translate3d("+a+"px,"+n+"px, 0)";if(i.camera.style!==u){o.style.WebkitTransform=u;o.style.MozTransform=u;o.style.oTransform=u;o.style.transform=u;i.camera.style=u}h(e,r)}};