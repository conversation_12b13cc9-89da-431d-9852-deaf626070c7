.addtocalender{
    background: #ffffff70;
    padding: 10px 8px;
    border-radius: 5px;
    border: 1px solid #3366ff;
    cursor:pointer;
}
input[type="date"]::-webkit-calendar-picker-indicator {
    background: transparent;
    bottom: 0;
    color: transparent;
    cursor: pointer;
    height: auto;
    left: 0;
    position: absolute;
    right: 0;
    top: 0;
    width: auto;
}

/*  */


/*  */

.select_duration{

    position: absolute;
    z-index: 1;
    border-radius: 4px !important;
    border: solid 1px #e4e9f2 !important;
    background: #F7F9FC !important;
    text-align: left;
    font-style: normal;
    font-weight: 600;
    color: #222B45 !important;
    font-size: 15px !important;
    height: auto !important;
    line-height: 20px !important;
    text-align-last: center;
}

.toclass{
    padding: 0.4375rem 0;
    color: #222B45 !important;
    font-size: 15px;
    font-weight: bold
}

.loader_schedule{
    margin-left: 30px !important;
    cursor: progress !important;
}

.banner_heading{
    flex: auto;
    margin-right: 12px;
}

.top_btns{
    display: flex;
    align-items: center;
}

.but2{
    visibility: visible;
    transition: color .15s ease-in-out,background-color .15s ease-in-out,border-color .15s ease-in-out,box-shadow .15s ease-in-out;
    font-family: Open Sans;
    font-size: 14px;
    font-weight: 700;
    color: #fff;
    width: 170px;
    height: 35px;
    border-radius: 4px;
    background-color: #36f;
    outline: 0;
    border: none;
    cursor: pointer;
}

.date_padding{
    padding-left: 0;
    padding-right: 0;
}

@media only screen and (max-width:768px){
    .date_padding{
        padding-left: 15px;
       padding-right: 15px;
    }
}

.topnav {
    padding: 0 20px;
    margin-left: 2%;
}

.topnav a {
    float: left;
    font-family: Open Sans;
    display: block;
    color: #000;
    text-align: center;
    padding: 14px 0px;
    margin-right: 40px!important;
    text-decoration: none;
    font-size: 14px;
    font-weight: 700;
    border-bottom: 3px solid transparent;
    color: #8f9bb3;
}

.topnav a:hover{
    color:#3366ff;
}
.topnav a.active {
    border-bottom: 4px solid #2979ff;
    color: #2979ff;
}

.meeting_separate{
    font-weight: bold;
    background-color: #e7e8e8;
    border-radius: 4px;
    font-family: 'Open Sans';
    font-size: 12px;
    color: #222b45;
    text-align: left;
    padding: 8px 10px 8px 10px;
    margin-bottom: 8px;
}

.timing_div{
    font-weight: bold;
    color: #222b45;
    font-size: 16px;
}

.meeting_topic{
    font-weight: bold;
    color: #222b45;
    font-size: 22px;
    padding-bottom: 8px;
}
.meeting_subtopic{
    color: #8f9bb3;
    font-size: 14px;
    font-weight: bold;
}

.meeting_edit_btn{
    border: 1px solid #e7e8e8;
    border-radius: 4px;
    padding: 4px 18px 4px 18px;
    width: 95px;
    font-weight: 500;
    height:35px;
    margin-bottom: 8px;
}

.meeting_details{
    cursor: pointer;
    transition: box-shadow .3s;
    box-shadow: 0 0 11px rgba(33,33,33,.2);
	margin-top: 16px;

}
.meeting_details:hover {
    box-shadow: 0 0 11px rgba(33,33,33,.2);
  }

.meeting_details:hover>.row>.col-md-3>label{
    color:#3366ff;
}
.meeting_details:hover>.row>.col-md-4>.meeting_topic{
    color:#3366ff;
}
.meeting_details:hover>.row>.col-md-4>.meeting_subtopic{
    color:#3366ff;
}
.meeting_details:hover>.row>.col-md-5{
    display: flex !important;
}

@media only screen and (max-width:768px){
    .meeting_details:hover>.row>.col-md-5{
        justify-content: flex-start !important;
        padding-bottom: 12px;
    }
}
.save_meeting_btn{
    background: #3366ff;
    color: #fff;
}

.edit_meeting_btn{
    color: #222b45;
    background: #fff;
}
/* .edit_meeting_btn:hover{
    background: #3366ff;
    color: #fff;
} */

option.date_ranges:hover{
    background: #3366ff !important;
    color:#fff !important;
}
.date_ranges{
    padding: 0px 0px 12px 0px;
    color: #222b45;
    font-weight: bold;
    width: 100%;
    text-align: center;
    background-color: #f7f9fc;
}


@media only screen and (max-width:364px){
    .top_btns{
        padding-top: 15px;
    }

    .main_banner{
        padding-bottom: 30px;
    }

    .sub_banner{
        flex-wrap: wrap;
    }
}

@media only screen and (min-width:992px){
    .navbar-translate{
        max-width: 25%;
    }
}
.popup_subheading {
    font-size: 15px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: normal;
    color: #8f9bb3;
}
.popup_heading {
    font-size: 18px;
    font-weight: bold;
    font-stretch: normal;
    font-style: normal;
    line-height: 1.33;
    letter-spacing: normal;
    color: #222b45;
    text-transform: none;
}

.deletemeeting-footer{
    display: flex;
    width: 100%;
    justify-content: flex-end;
}

@keyframes f {
	0% {
		opacity: .9
	}
	to {
		opacity: 0
	}
}
.scheduleselect.bootstrap-select .dropdown-menu li a{
    display: block;
    padding: 8px;
    margin: 0px;
    border-radius: 0;
}
.scheduleselect>.dropdown-toggle.btn-light::after{
    display:none;
}
.scheduleselect>.dropdown-toggle.btn.btn-link::after{
    display:none;
}
.scheduleselect.show>.btn.dropdown-toggle,.scheduleselect.show>.btn.dropdown-toggle:hover,.scheduleselect>.btn:focus{
    background: #F7F9FC;
    border: solid 1px #e4e9f2;
    border-color: #e4e9f2 !important;
}
.scheduleselect>.dropdown-toggle{
    margin-top: 0px;
    padding: 5px 12px;
    height: 36px;
    border-radius: 4px;
    border: solid 1px #e4e9f2;
    background: #F7F9FC;
    background-color: #F7F9FC !important;
    box-shadow: 0 2px 2px 0 rgb(153 153 153 / 14%), 0 3px 1px -2px rgb(153 153 153 / 20%), 0 1px 5px 0 rgb(153 153 153 / 12%) !important;
}
/* .bootstrap-select>select.bs-select-hidden,
select.bs-select-hidden,
select.selectpicker {
	display: none!important
} */
/* Style the select element */
.selectpicker {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px;
  width: 100%;
  cursor: pointer;
}

/* Style the select options in a grid format */
.selectpicker option {
  display: inline-block;
  width: 45px; /* Adjust the width as needed */
  padding: 5px;
  margin: 5px;
  border: 1px solid #ccc;
  text-align: center;
  cursor: pointer;
}
@media only screen and (max-width:920px){
	.time-slots {
		display: grid;
	grid-template-columns: repeat(4, 1fr);
	}
}
@media only screen and (max-width:650px){
	.time-slots {
		display: grid;
	grid-template-columns: repeat(3, 1fr);
	}
}
input[name=timeslot] {
	display: none
  }
.time-slots {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	grid-gap: 5px;
	margin-top: 20px;
  }

  .time-slot {
	padding: 12px 8px;
	border: 1px solid #5da3ed;
	text-align: center;
	cursor: pointer;
	font-size: 12px;
	font-weight: 600;
	position: relative;
	color: #007BFF;
	margin: 4px;
	border-radius: 4px;
  }

  .time-slot.selected {
	background-color: #007BFF;
	color: white;
  }

  /* .time-slot:hover {
	background-color: #f0f0f0;
	color: #007BFF;
  } */

  .live-indicator {
	position: absolute;
	margin-left: 2px;
	top: 40%;
	left: 0px;
	display: none;
	width: 5px; /* Adjust the size of the circle */
	height: 5px; /* Adjust the size of the circle */
	background-color: rgb(78, 228, 78);
	border-radius: 50%;
  }



.bootstrap-select {
	width: 220px;
	vertical-align: middle
}

.bootstrap-select>.dropdown-toggle {
	position: relative;
	width: 100%;
	text-align: right;
	white-space: nowrap;
	display: -ms-inline-flexbox;
	display: inline-flex;
	-ms-flex-align: center;
	align-items: center;
	-ms-flex-pack: justify;
	justify-content: space-between;
	padding-right: 25px;
	padding-left: 20px;
	z-index: 1
}

.bootstrap-select>.dropdown-toggle.bs-placeholder.btn,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn:hover {
	color: #fff
}



.bootstrap-select>.dropdown-toggle.bs-placeholder,
.bootstrap-select>.dropdown-toggle.bs-placeholder:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder:hover {
	color: #999
}

.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-danger,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-danger:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-danger:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-danger:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-dark,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-dark:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-dark:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-dark:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-info,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-info:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-info:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-info:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-primary,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-primary:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-primary:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-primary:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-secondary,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-secondary:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-secondary:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-secondary:hover,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-success,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-success:active,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-success:focus,
.bootstrap-select>.dropdown-toggle.bs-placeholder.btn-success:hover {
	color: hsla(0, 0%, 100%, .5)
}

.bootstrap-select>select {
	position: absolute!important;
	bottom: 0;
	left: 50%;
	display: block!important;
	width: .5px!important;
	height: 100%!important;
	padding: 0!important;
	opacity: 0!important;
	border: none;
	z-index: 0!important
}

.bootstrap-select>select.mobile-device {
	top: 0;
	left: 0;
	display: block!important;
	width: 100%!important;
	z-index: 2!important
}

.bootstrap-select.is-invalid .dropdown-toggle,
.error .bootstrap-select .dropdown-toggle,
.has-error .bootstrap-select .dropdown-toggle,
.was-validated .bootstrap-select select:invalid+.dropdown-toggle {
	border-color: #b94a48
}

.bootstrap-select.is-valid .dropdown-toggle,
.was-validated .bootstrap-select select:valid+.dropdown-toggle {
	border-color: #28a745
}

.bootstrap-select.fit-width {
	width: auto!important
}

.bootstrap-select:not([class*=col-]):not([class*=form-control]):not(.input-group-btn) {
	width: 100%;
}

.bootstrap-select .dropdown-toggle:focus,
.bootstrap-select>select.mobile-device:focus+.dropdown-toggle {
	outline: thin dotted #333!important;
	outline: 5px auto -webkit-focus-ring-color!important;
	outline-offset: -2px
}

.bootstrap-select.form-control {
	margin-bottom: 0;
	padding: 0;
	border: none;
	height: auto
}

:not(.input-group)>.bootstrap-select.form-control:not([class*=col-]) {
	width: 100%
}

.bootstrap-select.form-control.input-group-btn {
	float: none;
	z-index: auto
}

.form-inline .bootstrap-select,
.form-inline .bootstrap-select.form-control:not([class*=col-]) {
	width: auto
}

.bootstrap-select:not(.input-group-btn),
.bootstrap-select[class*=col-] {
	float: none;
	display: inline-block;
	margin-left: 0
}

.bootstrap-select.dropdown-menu-right,
.bootstrap-select[class*=col-].dropdown-menu-right,
.row .bootstrap-select[class*=col-].dropdown-menu-right {
	float: right
}

.form-group .bootstrap-select,
.form-horizontal .bootstrap-select,
.form-inline .bootstrap-select {
	margin-bottom: 0
}

.form-group-lg .bootstrap-select.form-control,
.form-group-sm .bootstrap-select.form-control {
	padding: 0
}

.form-group-lg .bootstrap-select.form-control .dropdown-toggle,
.form-group-sm .bootstrap-select.form-control .dropdown-toggle {
	height: 100%;
	font-size: inherit;
	line-height: inherit;
	border-radius: inherit
}

.bootstrap-select.form-control-lg .dropdown-toggle,
.bootstrap-select.form-control-sm .dropdown-toggle,
.input-group-lg>.bootstrap-select.form-control .dropdown-toggle,
.input-group-lg>.input-group-append>.bootstrap-select.btn .dropdown-toggle,
.input-group-lg>.input-group-append>.bootstrap-select.input-group-text .dropdown-toggle,
.input-group-lg>.input-group-prepend>.bootstrap-select.btn .dropdown-toggle,
.input-group-lg>.input-group-prepend>.bootstrap-select.input-group-text .dropdown-toggle,
.input-group-sm>.bootstrap-select.form-control .dropdown-toggle,
.input-group-sm>.input-group-append>.bootstrap-select.btn .dropdown-toggle,
.input-group-sm>.input-group-append>.bootstrap-select.input-group-text .dropdown-toggle,
.input-group-sm>.input-group-prepend>.bootstrap-select.btn .dropdown-toggle,
.input-group-sm>.input-group-prepend>.bootstrap-select.input-group-text .dropdown-toggle {
	font-size: inherit;
	line-height: inherit;
	border-radius: inherit
}

.bootstrap-select.form-control-sm .dropdown-toggle,
.input-group-sm>.bootstrap-select.form-control .dropdown-toggle,
.input-group-sm>.input-group-append>.bootstrap-select.btn .dropdown-toggle,
.input-group-sm>.input-group-append>.bootstrap-select.input-group-text .dropdown-toggle,
.input-group-sm>.input-group-prepend>.bootstrap-select.btn .dropdown-toggle,
.input-group-sm>.input-group-prepend>.bootstrap-select.input-group-text .dropdown-toggle {
	padding: .25rem .5rem
}

.bootstrap-select.form-control-lg .dropdown-toggle,
.input-group-lg>.bootstrap-select.form-control .dropdown-toggle,
.input-group-lg>.input-group-append>.bootstrap-select.btn .dropdown-toggle,
.input-group-lg>.input-group-append>.bootstrap-select.input-group-text .dropdown-toggle,
.input-group-lg>.input-group-prepend>.bootstrap-select.btn .dropdown-toggle,
.input-group-lg>.input-group-prepend>.bootstrap-select.input-group-text .dropdown-toggle {
	padding: .5rem 1rem
}

.form-inline .bootstrap-select .form-control {
	width: 100%
}

.bootstrap-select.disabled,
.bootstrap-select>.disabled {
	cursor: not-allowed
}

.bootstrap-select.disabled:focus,
.bootstrap-select>.disabled:focus {
	outline: none!important
}

.bootstrap-select.bs-container {
	position: absolute;
	top: 0;
	left: 0;
	height: 0!important;
	padding: 0!important
}

.bootstrap-select.bs-container .dropdown-menu {
	z-index: 1060
}

.bootstrap-select .dropdown-toggle .filter-option {
	position: static;
	top: 0;
	left: 0;
	float: left;
	height: 100%;
	width: 100%;
	text-align: left;
	overflow: hidden;
	-ms-flex: 0 1 auto;
	flex: 0 1 auto
}

.bs3.bootstrap-select .dropdown-toggle .filter-option {
	padding-right: inherit
}

.input-group .bs3-has-addon.bootstrap-select .dropdown-toggle .filter-option {
	position: absolute;
	padding-top: inherit;
	padding-bottom: inherit;
	padding-left: inherit;
	float: none
}

.input-group .bs3-has-addon.bootstrap-select .dropdown-toggle .filter-option .filter-option-inner {
	padding-right: inherit
}

.bootstrap-select .dropdown-toggle .filter-option-inner-inner {
	overflow: hidden
}

.bootstrap-select .dropdown-toggle .filter-expand {
	width: 0!important;
	float: left;
	opacity: 0!important;
	overflow: hidden
}

.bootstrap-select .dropdown-toggle .caret {
	position: absolute;
	top: 50%;
	right: 12px;
	margin-top: -2px;
	vertical-align: middle
}

.input-group .bootstrap-select.form-control .dropdown-toggle {
	border-radius: inherit
}

.bootstrap-select[class*=col-] .dropdown-toggle {
	width: 100%
}

.bootstrap-select .dropdown-menu {
	min-width: 100%;
	box-sizing: border-box
}

.bootstrap-select .dropdown-menu>.inner:focus {
	outline: none!important
}

.bootstrap-select .dropdown-menu.inner {
	position: static;
	float: none;
	border: 0;
	padding: 0;
	margin: 0;
	border-radius: 0;
	box-shadow: none
}

.bootstrap-select .dropdown-menu li {
	position: relative
}

.bootstrap-select .dropdown-menu li.active small {
	color: #fff!important
}

.bootstrap-select .dropdown-menu li.disabled a {
	pointer-events: none;
	cursor: not-allowed
}

.bootstrap-select .dropdown-menu li a {
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none
}

.bootstrap-select .dropdown-menu li a.opt {
	position: relative;
	padding-left: 2.25em
}

.bootstrap-select .dropdown-menu li a span.check-mark {
	display: none
}

.bootstrap-select .dropdown-menu li a span.text {
	display: inline-block
}

.bootstrap-select .dropdown-menu li small {
	padding-left: .5em
}

.bootstrap-select .dropdown-menu .notify {
	position: absolute;
	bottom: 5px;
	width: 96%;
	margin: 0 2%;
	min-height: 26px;
	padding: 3px 5px;
	background: #f5f5f5;
	border: 1px solid #e3e3e3;
	box-shadow: inset 0 1px 1px rgba(0, 0, 0, .05);
	pointer-events: none;
	opacity: .9;
	box-sizing: border-box
}

.bootstrap-select .dropdown-menu .notify.fadeOut {
	animation: .3s linear .75s forwards f
}

.bootstrap-select .no-results {
	padding: 3px;
	background: #f5f5f5;
	margin: 0 5px;
	white-space: nowrap
}

.bootstrap-select.fit-width .dropdown-toggle .filter-option {
	position: static;
	display: inline;
	padding: 0
}

.bootstrap-select.fit-width .dropdown-toggle .filter-option-inner,
.bootstrap-select.fit-width .dropdown-toggle .filter-option-inner-inner {
	display: inline
}

.bootstrap-select.fit-width .dropdown-toggle .bs-caret:before {
	content: "\00a0"
}

.bootstrap-select.fit-width .dropdown-toggle .caret {
	position: static;
	top: auto;
	margin-top: -1px
}

.bootstrap-select.show-tick .dropdown-menu .selected span.check-mark {
	position: absolute;
	display: inline-block;
	right: 15px;
	top: 10px;
	font-size: 16px
}

.bootstrap-select.show-tick .dropdown-menu li a span.text {
	margin-right: 34px
}



.bootstrap-select.show-menu-arrow.open>.dropdown-toggle,
.bootstrap-select.show-menu-arrow.show>.dropdown-toggle {
	z-index: 1061
}

.bootstrap-select.show-menu-arrow .dropdown-toggle .filter-option:before {
	content: "";
	border-left: 7px solid transparent;
	border-right: 7px solid transparent;
	border-bottom: 7px solid hsla(0, 0%, 80%, .2);
	position: absolute;
	bottom: -4px;
	left: 9px;
	display: none
}



.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle .filter-option:before {
	bottom: auto;
	top: -4px;
	border-top: 7px solid hsla(0, 0%, 80%, .2);
	border-bottom: 0
}



.bootstrap-select.show-menu-arrow.open>.dropdown-toggle .filter-option:after,
.bootstrap-select.show-menu-arrow.open>.dropdown-toggle .filter-option:before,
.bootstrap-select.show-menu-arrow.show>.dropdown-toggle .filter-option:after,
.bootstrap-select.show-menu-arrow.show>.dropdown-toggle .filter-option:before {
	display: block
}

.bootstrap-select .select-with-transition {
	background: no-repeat bottom, 50% calc(100% - 1px);
	transition: background 0s ease-out!important;
	background-size: 0 100%, 100% 100%;
	background-color: transparent!important;
	color: inherit!important;
	box-shadow: none!important
}

.bootstrap-select .btn.active,
.bootstrap-select .btn:active,
.bootstrap-select .select-with-transition {
	background-image: linear-gradient(0deg, #9c27b0 2px, rgba(156, 39, 176, 0) 0), linear-gradient(0deg, rgba(0, 0, 0, .26) 1px, transparent 0)
}

.bootstrap-select.show .select-with-transition:focus {
	background-size: 100% 100%, 100% 100%;
	transition-duration: .3s!important
}

.bootstrap-select .dropdown-toggle:focus {
	outline: none!important
}

.bootstrap-select.show>.dropdown-menu>.dropdown-menu {
	display: block
}

.bootstrap-select>.dropdown-menu>.dropdown-menu li.hidden {
	display: none
}

.data_range_dropdown{
	transform: rotate(0deg);
  transition: transform 150ms linear;
  will-change: transform;
}

.data_range_dropdown.open{
	transform: rotate(180deg);
	transition: transform 150ms linear;
	margin-left: 10px;
	will-change: transform;
}