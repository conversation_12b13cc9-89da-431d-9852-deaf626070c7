@function calc-top($line-height-base, $font-size, $component-height) {
  @return (($line-height-base * $font-size) - $component-height) / 2; // vertical center of line-height
}

// Emulate the less #contrast function
// TODO: this may be useful for the inverse theme, but if not, remove (it is unused after the removal of fullpalette)
// contrast-color and brightness borrowed from compass
// Copyright (c) 2009-2014 <PERSON>
// Complies with license: https://github.com/Compass/compass/blob/stable/LICENSE.markdown
@function contrast-color($color, $dark: $contrasted-dark-default, $light: $contrasted-light-default, $threshold: null) {
  @if $threshold {
    // Deprecated in Compass 0.13
    @warn "The $threshold argment to contrast-color is no longer needed and will be removed in the next release.";
  }

  @if $color == null {
    @return null;
  } @else {
    $color-brightness: brightness($color);
    $dark-text-brightness: brightness($dark);
    $light-text-brightness: brightness($light);
    @return if(abs($color-brightness - $light-text-brightness) > abs($color-brightness - $dark-text-brightness), $light, $dark);
  }
}

@function brightness($color) {
  @if type-of($color) == color {
    @return (red($color) * 0.299 + green($color) * 0.587 + blue($color) * 0.114) / 255 * 100%;
  } @else {
    @return unquote("brightness(#{$color})");
  }
}

@mixin linear-gradient($color1, $color2){
    background: $color1; /* For browsers that do not support gradients */
    background: -webkit-linear-gradient(60deg, $color1 , $color2); /* For Safari 5.1 to 6.0 */
    background: -o-linear-gradient(60deg, $color1, $color2); /* For Opera 11.1 to 12.0 */
    background: -moz-linear-gradient(60deg, $color1, $color2); /* For Firefox 3.6 to 15 */
    background: linear-gradient(60deg, $color1 , $color2); /* Standard syntax */
}

@mixin radial-gradient($extern-color, $center-color){
    background: $extern-color;
    background: -moz-radial-gradient(center, ellipse cover, $center-color 0%, $extern-color 100%); /* FF3.6+ */
    background: -webkit-gradient(radial, center center, 0px, center center, 100%, color-stop(0%,$center-color), color-stop(100%,$extern-color)); /* Chrome,Safari4+ */
    background: -webkit-radial-gradient(center, ellipse cover, $center-color 0%,$extern-color 100%); /* Chrome10+,Safari5.1+ */
    background: -o-radial-gradient(center, ellipse cover, $center-color 0%,$extern-color 100%); /* Opera 12+ */
    background: -ms-radial-gradient(center, ellipse cover, $center-color 0%,$extern-color 100%); /* IE10+ */
    background: radial-gradient(ellipse at center, $center-color 0%,$extern-color 100%); /* W3C */
    background-size: 550% 450%;
}

@mixin badges-color($color){
    .tag{
        background-color: $color;
        color: $white-color;

        .tagsinput-remove-link{
            color: $white-color;
        }
    }
}

@mixin create-colored-badges(){
    &.primary-badge{
        @include badges-color($primary);
    }
    &.info-badge {
        @include badges-color($info);
    }
    &.success-badge{
        @include badges-color($success);
    }
    &.warning-badge{
        @include badges-color($warning);
    }
    &.danger-badge{
        @include badges-color($danger);
    }
    &.rose-badge{
      @include badges-color($rose);
    }
}

@mixin badge-color() {
    &.badge-primary{
        background-color: $primary;
    }
    &.badge-info {
        background-color: $info;
    }
    &.badge-success{
        background-color: $success;
    }
    &.badge-warning{
        background-color: $warning;
    }
    &.badge-danger{
        background-color: $danger;
    }
    &.badge-rose{
      background-color: $rose;
    }
    &.badge-default{
        background-color: $gray-light;
    }
}
