body, html {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  height: 100%;
}
#root{
  height: 100%;
  display: flex;
  justify-content: center;
  align-content: center;

}
.enter-room-container{
  margin: auto;

}
.enter-room-container form{
  display: flex;
}
.enter-room-container input{
  height: 48px;
  background: transparent;
  color: var(--main-color-hover);
  font-size: 20px;
  outline: none;
  border:none;
  border-bottom: 2px solid var(--main-color-hover);
}
.enter-room-container button {
  background: var(--main-color-hover);
  width: 100px;
  border: none;
  margin-left: 24px;
  font-size: 20px;
  color: #252839;
  text-transform: uppercase;
  font-weight: bold;
}
.MainContentDiv{
  }

#myEnterVRButton{
    right: 16px;
    border-radius: 10px;
    padding: 8px 12px;
    background: rgb(0, 0, 0);
}
.Variation{
  margin: 8px;
  font-family: "Open Sans";
  text-transform: capitalize;
  color: rgb(255, 255, 255);
  border: none;
  padding: 3px 14px;
  border-radius: 6px;
}
@media only screen and (max-height:600px){
  .MainContentDiv{
  /* padding: 6px; */
  }
  .content_padding{
    padding:6px 0px 0px 10px !important ;
  }
  .MultiCarousel{
  }
  .relative-localvideo .user-video {
    height: 112.5px !important;
    width: 200px !important;
  }
  .relative-localvideo {
      position: fixed;
      top: 0rem !important;
      left: 0rem !important;
      transform: translate3d(0px,0px,0px);

  }

  .roomname {
    font-size: 15px;
    padding: 4px 5px !important;
    margin: 4px 4px !important;
  }
  #myEnterVRButton{
      right: 8px !important;
      border-radius: 10px;
      padding: 4px 6px !important;
      background: rgb(0, 0, 0);
  }
  .Variation {
    margin: 4px !important;
    font-family: "Open Sans";
    text-transform: capitalize;
    color: rgb(255, 255, 255);
    border: none;
    font-size: 12px;
    padding: 1px 7px !important;
    border-radius: 6px;
  }
  .menu_option {
    height: 37px!important;
    width: 42px !important;
  }
  }