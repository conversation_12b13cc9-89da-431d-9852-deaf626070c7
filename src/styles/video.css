.local-video-wrapper {
width: fit-content;
  
  display: flex;
  flex-direction: column;

}
.user-video {
  position: absolute;
    top: 0;
    left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
  background: #000;
}
.video-wrapper {
  height: 100vh;

  width: 100%;
}
.remote-video {
  height: 112px;
  right: 0;
  margin: 24px auto;
  display: inline-block;
  border-radius: 4px;
}
#remoteVideo.hide {
  display: none;
}
.video-wrapper .status {
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--main-color-hover);
  height: 100vh;
}

.controls{
  position: absolute;
  bottom: 24px;
  left: 24px;
}
.control-btn{
  position: relative;
  margin-right: 24px;
  background: transparent;
  outline: none;
  border: none;
  box-shadow: 1px 1px 8px black;
  border-radius: 50%;
  background-color: #252839;
  height: 64px;
  width: 64px;
}

@media screen and (max-width: 480px) {
  .video-wrapper {
    position: relative;
    max-width: 100vw;
  }
  /* .user-video {
    height: 80px;
  } */
  #remoteVideo {
    width: 100%;
    height: auto;
    margin-top: 20%;
  }
}
/* @media screen and (orientation: landscape) {
  .user-video {
    position: relative;
    left: 0;
    top: 0;
    margin-left: 0px;
    margin-top: 0px;
  }
} */
/* @media screen and (min-width: 768px) {
  .user-video {
    margin-left: 32px;
    margin-top: 12px;
  }
} */

.owl-carousel.owl-drag .owl-item{
  margin-right: 0px;
}