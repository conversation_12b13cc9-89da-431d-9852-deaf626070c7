/* Avatar styling for video cards */
.avatar-circle {
  position: relative;
  overflow: visible; /* Changed from hidden to allow waves to expand outward */
  transition: all 0.3s ease;
}

/* Speaking animation for avatar */
.avatar-speaking {
  position: relative;
}

/* Wave effect rings when speaking */
.avatar-speaking::before,
.avatar-speaking::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: 2px solid currentColor;
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(1);
  opacity: 0.8;
  z-index: 0;
  animation: wave-out 2s infinite cubic-bezier(0.1, 0.5, 0.5, 1);
}

.avatar-speaking::after {
  animation-delay: 0.5s;
}

/* Avatar content stays static */
.avatar-circle span {
  position: relative;
  z-index: 2;
  display: block;
  /* No animation applied to the actual avatar content */
}

/* Ocean wave animation */
@keyframes wave-out {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(2.5);
    opacity: 0;
  }
}

/* Ambient background effect */
.avatar-bg {
  position: absolute;
  inset: 0;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
  opacity: 0.7;
  z-index: 5;
}

/* Remove all pulse animations from the avatar itself */
.avatar-circle:not(.avatar-speaking) {
  animation: none;
  transform: scale(1);
}

/* Additional styling for the emanating waves */
.avatar-speaking .wave-ring {
  position: absolute;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid currentColor;
  opacity: 0;
  z-index: 1;
}

.avatar-speaking .wave-ring:nth-child(1) {
  animation: wave-out 2s infinite cubic-bezier(0.1, 0.5, 0.5, 1);
}

.avatar-speaking .wave-ring:nth-child(2) {
  animation: wave-out 2s infinite cubic-bezier(0.1, 0.5, 0.5, 1) 0.6s;
}

.avatar-speaking .wave-ring:nth-child(3) {
  animation: wave-out 2s infinite cubic-bezier(0.1, 0.5, 0.5, 1) 1.2s;
}