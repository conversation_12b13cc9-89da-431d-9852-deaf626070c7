import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './App.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import * as process from 'process';

window.global = window;
window.process = process;
window.Buffer = [];

if (process.env.NODE_ENV === 'PRODUCTION') {
  console.log = () => {}
  console.error = () => {}
  console.debug = () => {}
}
const root = ReactDOM.createRoot(document.getElementById("root"));
localStorage.setItem("version","0.6.6")
root.render(
   <div style={{width:"100%",background:'#fff'}}>
<App />  </div>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
