import React from "react";

import Share from "../ToolComponents/ShareModal";
import CloseModal from './CloseModal';
import FloorplanClient from "./FloorplanClient";
// import MapModal from "./Mapmodal";
import PDFclient from "./PDFclient";
import {connect} from "react-redux"
import * as HostActions from "../../Actions/HostAction"

class SceneControls extends React.Component {
  constructor(props){
    super(props);
    this.state={
      share:false,
      document:false,
      menu_bar:true,
      project:false,
      floorplan:false,
      data:this.props.data,
      pid:this.props.pid,
      close:false,
      map:false,
      floorplandata:"false",
      pdfdata:"false",
      settings:false,
      mapdata:"false"
    }
  }


  componentDidMount(){

    window.scrollTo(0, 0);


    this.setState({
        mapdata:this.props.mapdata
      })




  }
  componentDidUpdate(prevProps){
    if (this.props.mapdata !== prevProps.mapdata) {
      this.setState({
        mapdata:this.props.mapdata
      })
    }
  }
  // handleOutsideClick(e) {


  //   // ignore clicks on the component itself

  //   if(this.state.menu_bar && e.target.id!='menu_bar_up_icon'){
  //     this.setState({
  //       menu_bar:false
  //     })
  //     document.getElementById('menu_bar').classList.remove('menu_option_click');
  //     document.getElementById('tools_div').classList.remove('show');
  //     document.removeEventListener('click', this.handleOutsideClick, false);

  //   }else{
  //     document.addEventListener('click', this.handleOutsideClick, false);

  //     this.setState({
  //       menu_bar:true
  //     })
  //     document.getElementById('tools_div').classList.add('show');
  //     document.getElementById('menu_bar').classList.add('menu_option_click');
  //   }




  // }


open_close = (name,flag) =>{
//  document.getElementById('tools_div').classList.remove('show');

  this.setState({
    [name]:flag
  })
    if(flag){
    }
}
  render() {



return (

  <>





    <Share open_close={this.open_close} share={this.state.share} pid={this.state.pid} roomId={this.props.roomId} user_id={this.props.user_id} ></Share>

    <CloseModal destruct={this.props.destruct}  project={this.props.pid} room={this.props.roomId}  close={this.state.close} open_close={this.open_close} ></CloseModal>




      {this.props.floorplandata?<FloorplanClient data={this.props.floorplandata}/>:<></>}
      {this.props.pdfdata!=="false"?<PDFclient data={this.props.pdfdata}/>:<></>}
      {/* {this.props.mapdata!=="false"?<MapModal
              open_close={this.open_close}
              data={this.state.mapdata}
            />:<></>} */}



  </>


)}
}
const mapStateToProps = state => {
  return {

    Video:state.Call.Video,
    Audio:state.Call.Audio
  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, null)(SceneControls)
