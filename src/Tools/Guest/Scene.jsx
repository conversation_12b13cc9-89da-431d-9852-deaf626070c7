import React from 'react';
// import * as THREE from 'three'
import {connect} from "react-redux";
import VR<PERSON> from "./VRUI"
import {Resolvepath} from "../../components/Resolvepath"
import InfoEntity from "./InfoEntity"
// import LoadingScreen from "react-loading-screen";
const THREE = window.THREE

class Scene extends React.Component {

    constructor(props)
    {
      super(props);
      this.state = {
        loaded:false,
        VRMode:false,
        imageload:true,
        anirotation:"0 0 0",
        thumb:true,
        InVR:false
    };
    this.first = true;
    this.total = 0;
    this.assets = [];
    this.clientAssets = [];
    // this.change = this.change.bind(this);
  // this.imageloaded=this.imageloaded.bind(this);
}
componentDidMount(){
  document.querySelector('a-scene').addEventListener('enter-vr',  ()=> {
this.setState({InVR:true})
  })
  document.querySelector('a-scene').addEventListener('exit-vr', ()=> {
this.setState({InVR:false})

  })
}
imageloaded = () => {
  this.setState({
    imageload:false,
    thumb:false
  })
}


Thumbloaded = () => {
  this.props.loader();
  this.setState({
    imageload:false,
  })
  this.first = false
}

  loadAssets = () => {
    if(!this.assets.includes(this.props.currentnode.imageid))
    {this.setState({
      imageload:true,
      thumb:true
    })
      this.assets.push(this.props.currentnode.imageid)
      this.assets.push(this.props.currentnode.imageid+'Thumb')

      this.clientAssets.push(
        <img
          crossOrigin="anonymous"
          id={this.props.currentnode.imageid}
          src={Resolvepath(this.props.currentnode.currentimage)}
          alt={this.props.currentnode.imageid}
          key={this.props.currentnode.imageid}
          onLoad={this.imageloaded}
        />
        )

        this.clientAssets.push(
        <img
          crossOrigin="anonymous"
          id={this.props.currentnode.imageid+'Thumb'}
          src={Resolvepath(this.props.currentnode.currentimageThumb)}
          alt={this.props.currentnode.imageid}
          key={this.props.currentnode.imageid+'Thumb'}
          onLoad={this.Thumbloaded}
        />
        )

    }
  }

  render()
    {
      this.loadAssets()
      return(
        <>
      <a-scene  embedded device-orientation-permission-ui="enabled: false" loading-screen="enabled:false" vr-mode-ui="enabled:true;enterVRButton: #myEnterVRButton" gltf-model="dracoDecoderPath: https://propvr.tech/draco/" style={{position:"relative",display:this.props.platform?"block":"none"}}  >

      {/* Loads Assets a*/}

      <a-assets>
        {this.clientAssets}


      </a-assets>
      <a-assets>
        <a-asset-item id="Modelglb" src="https://storagecdn.propvr.tech/0SalestoolAssets%2Favatar.glb"></a-asset-item>
        <img id="Highlighter" crossOrigin="anonymous" src={"https://cdn.propvr.tech/images/Highlighter.png"} defscale="0.24 0.24"></img></a-assets>
      {this.props.ProjectSettings!=undefined && this.props.ProjectSettings.stereotype!==undefined && this.props.ProjectSettings.stereotype===1?  <a-entity id="asd" rotation="0 0 0" overunder={this.props.currentnode.currentimage}></a-entity>:
            <>{this.state.thumb?
              <a-sky src= {'#'+this.props.currentnode.imageid+'Thumb'} /> :
              <a-sky src= {'#'+this.props.currentnode.imageid} />
            } </>}

{this.props.entityinfo?
  <a-image
                  look-at='#cam1'
                  class="hotspot"
                  src={this.props.entityinfo.icon}
                  scale={this.props.entityinfo.scale}
                  look-at="#cam1"
                  position={this.props.entityinfo.position}>
                </a-image>
:""}
 {this.props.info?
<InfoEntity Info={this.props.info}/>:""}
<a-entity  laser-controls="hand: left" raycaster="objects:.hotspots" daydream-controls="" gearvr-controls="" magicleap-controls="" oculus-go-controls="" oculus-touch-controls="" valve-index-controls="" vive-controls="" vive-focus-controls="" windows-motion-controls="" generic-tracked-controller-controls=""></a-entity>
<a-entity  laser-controls="hand: right" raycaster="objects:.hotspots" daydream-controls="" gearvr-controls="" magicleap-controls="" oculus-go-controls="" oculus-touch-controls="" valve-index-controls="" vive-controls="" vive-focus-controls="" windows-motion-controls="" generic-tracked-controller-controls=""></a-entity>

{this.state.imageload && !this.props.loading?
              <div className="imageswitch" style={{ backgroundColor: 'black' }}>
                <div className="switch_project_loader2">
                  <span>
                    <svg className="switch_project_svg" width="20" height="20" viewBox="0 0 20 20">
                      <path className="svgicons-reseller" fillRule="evenodd" d="M6.659.473L7.977 0l.947 2.637-1.319.473C4.748 4.134 2.802 6.854 2.802 9.943c0 4.007 3.246 7.256 7.248 7.256 3.103 0 5.833-1.971 6.842-4.856l.463-1.322 2.645.925-.463 1.322c-1.4 4-5.183 6.732-9.487 6.732C4.5 20 0 15.497 0 9.943c0-4.28 2.697-8.049 6.659-9.47z"></path>
                    </svg>
                  </span>

                  <span style={{ paddingLeft: "16px", marginTop: "-25px", color: 'white' }}>
                    Loading image...
                  </span>
                </div>
              </div> :<></>}
      <a-entity id="annotate-holder" position="0 0 0"></a-entity>
      {/* Loads Mouse */}

      {/* <a-entity position="0 0 0" id="PIN_VIDEO">
      {this.state.InVR?
      <PINVIDEO/>
      :""}
            </a-entity> */}
            {this.state.InVR? <VRUI/>:""}
      <a-camera
        id="cam1"
        rotation="0 0 0"
        modlook-controls=""
        rotation-reader
        cursor="rayOrigin: mouse; fuse: false;"
        listen-camera="" stereocam="eye:left" cursor-visible="false" >

<a-entity position="0 0 -5" radius="1.25" color="#EF2D5E" id="MYVIEW"></a-entity>

      </a-camera>
      {/* <Users/> */}
      {/* <a-entity  gltf-model="https://storagecdn.propvr.tech/0SalestoolAssets%2Favatar.glb" look-at='#cam1' shadow="cast: true" scale="25 25 25" position="0 -65 100" changeshader="" applyhdr="" instanced-mesh="modelname: null; rootname: null" loadmesh=""></a-entity>
      <a-entity  gltf-model="https://storagecdn.propvr.tech/0SalestoolAssets%2Favatar.glb" look-at='#cam1' shadow="cast: true" scale="25 25 25" position="-100 -65 0" changeshader="" applyhdr="" instanced-mesh="modelname: null; rootname: null" loadmesh=""></a-entity>
      <a-entity  gltf-model="https://storagecdn.propvr.tech/0SalestoolAssets%2Favatar.glb" look-at='#cam1' shadow="cast: true" scale="25 25 25" position="100 -65 0" changeshader="" applyhdr="" instanced-mesh="modelname: null; rootname: null" loadmesh=""></a-entity> */}

  </a-scene>
  <div
       id="myEnterVRButton"
       className="m-075 cursor-pointer align-items-center justify-content-center position-fixed top-16"

     >
       <svg height={24} width={24} fill="#fff" viewBox="0 0 32 32" role="img">
         <path
           d="M8 5C6.172 5 4.996 6.074 4.5 7.063c-.2.394-.305.769-.375 1.093C2.906 8.54 2 9.664 2 11v13c0 1.645 1.355 3 3 3h7c1.32 0 2.52-.797 3.063-2h1.874c.54 1.203 1.743 2 3.063 2h7c1.645 0 3-1.355 3-3V11c0-1.336-.906-2.46-2.125-2.844c-.07-.324-.176-.699-.375-1.094C27.004 6.079 25.82 5 24 5zm0 2h16c1.148 0 1.457.422 1.719.938L25.75 8H6.25l.031-.063C6.54 7.426 6.84 7 8 7zm-3 3h22c.566 0 1 .434 1 1v13c0 .566-.434 1-1 1h-7a1.361 1.361 0 0 1-1.25-.844l-1.031-2.75v-.031l-.032-.031a1.871 1.871 0 0 0-1.718-1.157a1.91 1.91 0 0 0-1.75 1.157l-.031.031v.063l-.938 2.718A1.365 1.365 0 0 1 12 25H5c-.566 0-1-.434-1-1V11c0-.566.434-1 1-1zm5 3c-2.2 0-4 1.8-4 4s1.8 4 4 4s4-1.8 4-4s-1.8-4-4-4zm12 0c-2.2 0-4 1.8-4 4s1.8 4 4 4s4-1.8 4-4s-1.8-4-4-4zm-12 2c1.117 0 2 .883 2 2s-.883 2-2 2s-2-.883-2-2s.883-2 2-2zm12 0c1.117 0 2 .883 2 2s-.883 2-2 2s-2-.883-2-2s.883-2 2-2zm-6.031 7.438l.219.562h-.407z"
           fill="#fff"
         />
       </svg>
     </div>
  </>
        );
    }
}

const mapStateToProps=(state)=>{
  return {
    HostStatus:state.Call.HostStatus,
  }
}
export default connect(mapStateToProps,null)(Scene);
const ModelPostions=["0 0 -100", "-100 0 0","0 0 100","100 0 0"]
class UsersClass extends React.Component{

  constructor(props){
    super(props);
  }
  render(){
    return(
      <>
      {Object.values(this.props.Peers).map((peer,index) => { if (peer != null) {
       return (
         <UserModel Peer={peer} position={ModelPostions[index]}/>

        )}else{
return ""
      }})}
      </>
    )
  }
}

const mapStateToPropsUsers=(state)=>{
  return{
    Peers: state.Call.peers,
    userVideoAudio:state.Call.userVideoAudio,
  }
}
const Users =connect(mapStateToPropsUsers,null)(UsersClass)
class UserModel extends React.Component{
  constructor(props){
    super(props);

  }
  componentDidMount(){
    document.getElementById(this.props.Peer.peerID+"ModelGLB").addEventListener("model-loaded", e =>{
      document.getElementById(this.props.Peer.peerID+"ModelGLB").getObject3D('mesh').traverse(function(node){
        if(node.name==="KID OK.006_1"){
        const Color = new THREE.Color("#" + this.props.Peer.extra.color)
          node.material.color=Color;


        }
      })

    });


  }
  render(){
          // <a-entity id={this.props.Peer.peerID+"ModelGLB"} gltf-model="https://storagecdn.propvr.tech/0SalestoolAssets%2Favatar.glb" look-at='#cam1'  animation-mixer="clip:idle c" shadow="cast: false" scale="18.75 18.75 18.75" position={this.props.position} changeshader="" applyhdr="" instanced-mesh="modelname: null; rootname: null" loadmesh=""></a-entity>

    return(<>
      <a-entity  position={this.props.position}>
      <a-entity id={this.props.Peer.peerID+"_VIDEOSPHERE"} position={"0 0 0"}>
      </a-entity>
      </a-entity>

      <a-entity id={this.props.Peer.peerID+"ModelGLB"} look-at={"#"+this.props.Peer.peerID+"_VIDEOSPHERE"} gltf-model="#Modelglb"   shadow="cast: false" scale="18.75 18.75 18.75" position={this.props.position} changeshader="" applyhdr="" instanced-mesh="modelname: null; rootname: null" loadmesh="">

      </a-entity>
      </>

    )
  }
}
