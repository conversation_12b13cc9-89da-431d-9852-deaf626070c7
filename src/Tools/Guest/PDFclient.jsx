import React from "react";
// import { Redirect, Route, Link } from "react-router-dom";
// import Fire from "../../config/Firebase.jsx";

// import $ from 'jquery';
// import PDFViewer from 'mgr-pdf-viewer-react'

class Pdfviewer extends React.Component {
  constructor(props){
    super(props);
    this.state={
      url:'/salestool/pdf/pdf.html?data='+this.props.data+'&type=quest'
  }
   this.ExamplePDFViewer = this.ExamplePDFViewer.bind(this);
}
componentDidUpdate(nextProps) {
  if (this.props.data !== nextProps.data) {
    this.setState({
      url:'/salestool/pdf/pdf.html?data='+this.props.data+'&type=quest'
    })
  }
}
componentDidMount(){
    
    window.scrollTo(0, 0);
    // var data = this.props.data;
  
    //document.getElementById('pdf_viewer').style.display='block'; 

  }

 
  ExamplePDFViewer(){
    // const style={
    //   height:'300px'
    // }
    const iframe = {
      height: "100%",
    width: "100%",
    border: "none",
    borderRadius: "10px",
    background: "#fff"
    }
    return (
      <iframe id="iframe" style={iframe} src={this.state.url}></iframe>

        // <PDFViewer
        //     document={{
        //         url: this.props.data
                
        //     }}
        //     css="pdf_height"
        //     navigation={{
        //       css: {
        //         previousPageBtn: ('pdf_navigation_host'),  
        //         nextPageBtn: ('pdf_navigation_host')
        //     }
               
        //     }}
        // />
    )
}

  
 
 






  render() {

    const modal={
      width: "100%",
      height: "100%",
      maxWidth: "100%",
      margin: "0",
      padding: "0"
    }

    const modal_content={
      height: "95%",
    width: "95%",
    borderRadius: "10px!important"
    }

    const modal_body={
      height: "100%",
    width: "100%",
    border: "none"
    }

      return( 
        <div style={{'display':'block'}} className="modal" id="pdf_modal" tabIndex="-1" role="dialog">
        <div style={modal} className="modal-dialog" role="document">
          <div style={modal_content} className="modal-content br_10">   
              <div style={modal_body} id="df_manual_book">
            
              {this.ExamplePDFViewer()}
              {/* <ExamplePDFViewer></ExamplePDFViewer> */}
	        </div>  
          </div>
        </div>
        </div>
      )
  }
}
export default Pdfviewer;
