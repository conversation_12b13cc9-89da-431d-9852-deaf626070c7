import React from "react"
class InventoryInfo extends React.Component{

    constructor(props){
        super(props)
    }
    
    render(){
        return (
<div className="position-absolute text-white user_select_cls inventory_card_html" style={{zIndex: 11111, width:"96%",maxWidth:"373px", backdropFilter: 'blur(5px)', top: '50%', left: '50%',transform:"translate(-50%,-50%)"}}>

  <div className="row m-0 flex-column" style={{background: 'rgba(0,0,0,.5)', height: 'fit-content', maxHeight: '150px', borderTopRightRadius: '10px', borderTopLeftRadius: '10px'}}>
    <div className="h-100 w-100">
      <img className="h-100 w-100 p-0" style={{objectFit: 'cover', maxHeight: '150px', borderBottomLeftRadius: '0px', borderBottomRightRadius: '0px'}} src="https://storagecdn.propvr.tech/Emami_inv_images/Emami Aastha prasad EastFacing.png?alt=media" />
    </div>
  </div>
  <div className="row m-0 flex-column" style={{background: 'rgba(0,0,0,.5)', height: 'fit-content', maxHeight: '80px'}}>
    <div className="p-3">
      <div className="row m-0 mb-3" style={{flexWrap: 'nowrap'}}>
        <div style={{padding:"0px"}} className="col-3">
          <p className="m-0">Unit No.</p>
          <p className="m-0" style={{whiteSpace:"nowrap"}}>Type</p>

        </div>
        <div style={{padding:"0px"}} className="col-4">
          <p className="m-0">{this.props.info.unitname}</p>
          <p className="m-0">{this.props.info.unittype}</p>
        </div>
        <div style={{padding:"0px"}} className="col-5">
          <div className="d-flex align-items-center bg-dark py-1 px-2 cursor-pointer" style={{borderRadius: '25px', maxWidth: '120px'}}> 
            <span>
              <svg height={20} width={20} viewBox="0 0 24 24" fill="#fff"><g data-name="Layer 2"><g data-name="cube"><rect width={24} height={24} opacity={0} /><path d="M20.66 7.26c0-.07-.1-.14-.15-.21l-.09-.1a2.5 2.5 0 0 0-.86-.68l-6.4-3a2.7 2.7 0 0 0-2.26 0l-6.4 3a2.6 2.6 0 0 0-.86.68L3.52 7a1 1 0 0 0-.15.2A2.39 2.39 0 0 0 3 8.46v7.06a2.49 2.49 0 0 0 1.46 2.26l6.4 3a2.7 2.7 0 0 0 2.27 0l6.4-3A2.49 2.49 0 0 0 21 15.54V8.46a2.39 2.39 0 0 0-.34-1.2zm-8.95-2.2a.73.73 0 0 1 .58 0l5.33 2.48L12 10.15 6.38 7.54zM5.3 16a.47.47 0 0 1-.3-.43V9.1l6 2.79v6.72zm13.39 0L13 18.61v-6.72l6-2.79v6.44a.48.48 0 0 1-.31.46z" /></g></g></svg>
            </span>
            <span className="ml-2" style={{fontSize: '12px'}}>Interiors</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div className="row m-0 flex-column text-dark bg-white" style={{borderBottomLeftRadius: '10px', borderBottomRightRadius: '10px'}}>
    <div className="p-3">
      <div className="row m-0 pb-2" style={{borderBottom: '1px solid #000'}}>
        <div className="d-flex align-items-center mr-3 mb-2">
          <div className="mr-1">
            <svg width={30} height={30} preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"><path d="M21 10.78V8c0-1.65-1.35-3-3-3h-4c-.77 0-1.47.3-2 .78c-.53-.48-1.23-.78-2-.78H6C4.35 5 3 6.35 3 8v2.78c-.61.55-1 1.34-1 2.22v5c0 .55.45 1 1 1s1-.45 1-1v-1h16v1c0 .55.45 1 1 1s1-.45 1-1v-5c0-.88-.39-1.67-1-2.22zM14 7h4c.55 0 1 .45 1 1v2h-6V8c0-.55.45-1 1-1zM5 8c0-.55.45-1 1-1h4c.55 0 1 .45 1 1v2H5V8zm-1 7v-2c0-.55.45-1 1-1h14c.55 0 1 .45 1 1v2H4z" fill="#000" /></svg>  
          </div>
          <div className="ml-1">
            <p className="m-0" style={{color: '#5d5d5d', fontWeight: 600}}>Bedroom</p>
            <p className="m-0">{this.props.info.beds}</p>
          </div>
        </div> 
        <div className="d-flex align-items-center mr-3 mb-2">
          <div className="mr-1">
            <svg width={30} height={30} preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"><path fill="#000" d="M464 280H80V100a51.258 51.258 0 0 1 15.113-36.485l.4-.4a51.691 51.691 0 0 1 58.6-10.162a79.1 79.1 0 0 0 11.778 96.627l10.951 10.951l-20.157 20.158l22.626 22.626l20.157-20.157L311.157 71.471l20.157-20.157l-22.627-22.627l-20.158 20.157l-10.951-10.951a79.086 79.086 0 0 0-100.929-8.976A83.61 83.61 0 0 0 72.887 40.485l-.4.4A83.054 83.054 0 0 0 48 100v180H16v32h32v30.7a23.95 23.95 0 0 0 1.232 7.589L79 439.589A23.969 23.969 0 0 0 101.766 456h12.9L103 496h33.333L148 456h208.1l12 40h33.4l-12-40h20.73A23.969 23.969 0 0 0 433 439.589l29.766-89.3A23.982 23.982 0 0 0 464 342.7V312h32v-32zM188.52 60.52a47.025 47.025 0 0 1 66.431 0L265.9 71.471L199.471 137.9l-10.951-10.949a47.027 47.027 0 0 1 0-66.431zM432 341.4L404.468 424H107.532L80 341.4V312h352z" /></svg>
          </div>
          <div className="ml-1">
            <p className="m-0" style={{color: '#5d5d5d', fontWeight: 600}}>Bathroom</p>
            <p className="m-0">{this.props.info.baths}</p>
          </div>
        </div>
        <div className="d-flex align-items-center mb-2">
          <div className="mr-1">
            <svg viewBox="0 0 480.192 480.192" height={30} width={30}>
              <g><g><path d="M472.096,0.192h-120c-4.424,0-8,3.584-8,8v80v104c0,4.416,3.576,8,8,8h40h40h32v16h-88c-4.424,0-8,3.584-8,8s3.576,8,8,8    h88v56h-24v-24c0-4.416-3.576-8-8-8h-32c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8h24v24v80h-24c-4.424,0-8,3.584-8,8    c0,4.416,3.576,8,8,8h32h32v16h-80c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8h80v40h-80c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8    h88c4.424,0,8-3.584,8-8v-56v-32v-88v-72v-32v-104v-80C480.096,3.776,476.52,0.192,472.096,0.192z M464.096,376.192h-24v-72h24    V376.192z M384.096,112.192v72h-24v-88h24V112.192z M424.096,184.192h-24v-64h24V184.192z M464.096,184.192h-24v-72v-16h24    V184.192z M464.096,80.192h-32c-4.424,0-8,3.584-8,8v16h-24v-16c0-4.416-3.576-8-8-8h-32v-64h104V80.192z" /></g></g>
              <g><g><path d="M360.096,272.192h-16v-32c0-13.232-10.768-24-24-24h-128h-128c-13.232,0-24,10.768-24,24v32h-16    c-13.232,0-24,10.768-24,24v16v40c0,13.232,10.768,24,24,24v32v16c0,10.416,6.712,19.216,16,22.528v17.472h-8    c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8h16h32h16c4.424,0,8-3.584,8-8c0-4.416-3.576-8-8-8h-8v-16h104h104v16h-8    c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8h16h32h16c4.424,0,8-3.584,8-8c0-4.416-3.576-8-8-8h-8V446.72    c9.288-3.312,16-12.112,16-22.528v-16v-32c13.232,0,24-10.768,24-24v-40v-16C384.096,282.96,373.328,272.192,360.096,272.192z     M16.096,296.192c0-4.408,3.584-8,8-8h24v16h-32V296.192z M32.096,360.192h-8c-4.416,0-8-3.592-8-8v-32h32v40H32.096z     M72.096,464.192h-16v-16h16V464.192z M184.096,432.192h-104h-32c-4.416,0-8-3.592-8-8v-8h144V432.192z M184.096,400.192h-144v-24    h16h128V400.192z M184.096,360.192h-120v-48v-32c0-4.416-3.576-8-8-8v-32c0-4.408,3.584-8,8-8h120V360.192z M200.096,232.192h120    c4.416,0,8,3.592,8,8v32c-4.424,0-8,3.584-8,8v32v48h-120V232.192z M328.096,464.192h-16v-16h16V464.192z M344.096,424.192    c0,4.408-3.584,8-8,8h-32h-104v-16h144V424.192z M344.096,400.192h-144v-24h128h16V400.192z M368.096,352.192c0,4.408-3.584,8-8,8    h-8h-16v-40h32V352.192z M368.096,304.192h-32v-16h24c4.416,0,8,3.592,8,8V304.192z" /></g></g>
              <g><g><path d="M136.096,280.192h-8v-8c0-4.416-3.576-8-8-8s-8,3.584-8,8v8h-8c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8h8v8    c0,4.416,3.576,8,8,8s8-3.584,8-8v-8h8c4.424,0,8-3.584,8-8C144.096,283.776,140.52,280.192,136.096,280.192z" /></g></g>
              <g><g><path d="M288.096,280.192h-8v-8c0-4.416-3.576-8-8-8s-8,3.584-8,8v8h-8c-4.424,0-8,3.584-8,8c0,4.416,3.576,8,8,8h8v8    c0,4.416,3.576,8,8,8s8-3.584,8-8v-8h8c4.424,0,8-3.584,8-8C296.096,283.776,292.52,280.192,288.096,280.192z" /></g></g><g><g><path d="M216.992,93.576l-15.648-45.232c0.712-2.96,0.76-5.992,0.76-7.624c0-22.832-18.448-40.72-42-40.72s-42,18.072-42,41.144    c0,2.76,0.264,5.104,0.736,7.104l-16.608,45.184c-0.904,2.456-0.544,5.192,0.944,7.336c1.504,2.144,3.952,3.424,6.568,3.424    h99.688c2.584,0,5.016-1.248,6.512-3.36C217.44,98.72,217.832,96.016,216.992,93.576z M160.104,16.008    c14.632,0,25.72,10.36,25.992,24.184H134.12C134.624,26.552,145.856,16.008,160.104,16.008z M121.208,88.192l11.768-32h54.16    l11.072,32H121.208z" /></g></g>
              <g><g><path d="M100.096,139.408c-3.824-2.216-8.72-0.904-10.928,2.928l-12.616,21.856c-2.216,3.832-0.904,8.72,2.928,10.928    c1.256,0.728,2.632,1.072,3.992,1.072c2.768,0,5.456-1.432,6.936-4l12.616-21.856C105.232,146.504,103.92,141.616,100.096,139.408    z" /></g></g>
              <g><g><path d="M239.024,164.192l-12.616-21.856c-2.216-3.832-7.12-5.144-10.928-2.928c-3.832,2.208-5.144,7.104-2.928,10.928    l12.616,21.856c1.488,2.568,4.168,4,6.936,4c1.36,0,2.736-0.344,3.992-1.072C239.928,172.912,241.24,168.016,239.024,164.192z" /></g></g>
              <g><g><path d="M160.096,136.192c-4.424,0-8,3.584-8,8v24c0,4.416,3.576,8,8,8s8-3.584,8-8v-24    C168.096,139.776,164.52,136.192,160.096,136.192z" /></g></g>
            </svg> 
          </div>
          <div className="ml-1">
            <p className="m-0" style={{color: '#5d5d5d', fontWeight: 600}}>Sqft</p>
            <p className="m-0">{this.props.info.sqft}</p>
          </div>
        </div>
      </div> 
      <div className="row m-0 my-3">
        <div className="col-6">
          <b>Pricing</b>
        </div>
        <div className="col-6 text-right">
          <b>INR {this.props.info.price}</b>
        </div>
      </div>
    </div></div>

    </div>

        )
    }
}

export default InventoryInfo