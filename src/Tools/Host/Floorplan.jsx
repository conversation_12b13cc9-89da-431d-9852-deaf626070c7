import React from "react";
// import { Redirect, Route, Link } from "react-router-dom";
// import Fire from "../../config/Firebase.jsx";

import Select from 'react-select';
import { Resolvepath } from "../../components/Resolvepath"
class Flooplan extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      default: [],
      floorplan_name: [],
      floorplan_list: [],
      selectedOption: 0,
      selectedImage:"",
      data: this.props.data,
      annotate: false,
      openFloreplanView: false,
      screenHeight: window.innerHeight,
      isLandscape: window.innerWidth > window.innerHeight,
    }
    this.changesky = this.changesky.bind(this);
    this.MapAnnotate = this.MapAnnotate.bind(this);
  }

  updateScreenHeight = () => {
    this.setState({
      screenHeight: window.innerHeight
    });
  };

  handleOrientationChange = () => {
    const isLandscape = window.innerWidth > window.innerHeight;
    this.setState({ isLandscape });
  };
  componentDidMount() {
    // Add an event listener to update the screen height on window resize
    window.addEventListener('resize', this.updateScreenHeight);
    window.addEventListener('resize', this.handleOrientationChange);
  }

  componentWillUnmount() {
    // Remove the event listener when the component is unmounted to avoid memory leaks
    window.removeEventListener('resize', this.updateScreenHeight);
    window.removeEventListener('resize', this.handleOrientationChange);
  }


  handleChange = selectedOption => {
    var a = document.querySelectorAll('.map_div');
    [].forEach.call(a, function (el) {
      el.style.display = 'none';
    });
    this.props.senddata({ actiontype: "floorplan", roomid: this.props.room, data: this.state.data.plans[selectedOption.value], pin: null, annotate: false })
    //this.props.socket.emit('floorplan',{ roomid:this.props.room,data:this.state.data.plans[selectedOption.value],pin:null});
    var a = [
      {
        value: selectedOption.value,
        label: selectedOption.label
      }
    ];

    this.setState({
      selectedOption: a
    });
    document.getElementById(selectedOption.value).style.display = 'block';
  };

  handleSelect(val,index) {
    // this.props.senddata({ actiontype: "floorplan", roomid: this.props.room, data: this.state.data.plans[val.id], pin: null, annotate: false })
    this.setState({
      selectedOption: index,
      selectedImage:val
    });
  }

  handleregister() {

  }

  changesky(event) {


    var a = document.querySelectorAll('.bounce');
    [].forEach.call(a, function (el) {
      el.classList.remove("bounce");
    });
    var key = event.target.id.split("_pins");
    this.props.changeImage(key[0]);

    this.props.senddata({ actiontype: "floorplan", roomid: this.props.room, data: this.state.data.plans[this.state.selectedOption[0].value], annotate: false, pin: event.target.style.top + "-" + event.target.style.left })


    document.getElementById(event.target.id).classList.add('bounce');
    //   for(var i =0; i<key.length; i++){
    //     if(list[i].url === event.target.id){


    //     }
    //   }
  }
  MapAnnotate(event) {

    this.props.senddata({
      actiontype: "floorplan", roomid: this.props.room, data: this.state.data.plans[this.state.selectedOption.id], pin: null,
      annotate: {
        right: event.clientX - 20,
        // (event.clientX/event.currentTarget.width)*100,
        bottom: event.clientY - 46 - 50,
        // (event.clientY/event.currentTarget.height)*100
      }
    })



    this.setState({ annotate: { top: event.clientY - 46 - 50, left: event.clientX - 20 } })
  }

  handleFloreplanViewOpen(val) {
    this.setState({
      openFloreplanView: val
    })
    this.props.senddata({ actiontype: "floorplan", roomid: this.props.room, data: this.state.floorplan_list[this.state.selectedOption], annotate: false, pin: null })

    // this.props.open_close('floorplan', !val)

  }
  componentDidMount() {

    window.scrollTo(0, 0);
    var temp = [];
    var temp_floor = [];
    if (this.state.data.hasOwnProperty('plans')) {
      var obj = Object.keys(this.state.data.plans);
      var list = Object.values(this.state.data.plans);

      for (var i = 0; i < obj.length; i++) {
        temp.push({
          value: obj[i],
          label: list[i].name
        })

        var pins = [];
        var j;
        var k;
        for (j in list[i]['pins']) {
          for (k in this.props.data.images) {
            if (this.props.data.images[k].url === list[i]['pins'][j].dest) {
              pins.push({
                x: list[i]['pins'][j].posx,
                y: list[i]['pins'][j].posy,
                dest: list[i]['pins'][j].dest,
                id: k + "_pins" + j
              })
            }
          }

        }

        temp_floor.push({
          index: i,
          id: obj[i],
          name: list[i].name,
          url: list[i].planurl,
          pins: pins
        })
      }
    }
    this.setState({
      floorplan_list: temp_floor,
      floorplan_name: temp,
      selectedImage: temp_floor[0]
    })



  }


  render() {
    
    const customStyles = {
      control: () => ({
        alignItems: 'center',
        backgroundColor: '#fff',
        borderRadius: '4px',
        display: 'flex',
        webkitFlexWrap: 'wrap',
        flexWrap: 'wrap',
        webkitBoxPack: 'justify',
        webkitJustifyContent: 'space-between',
        msFlexPack: 'justify',
        justifyContent: 'space-between',
        minHeight: '38px',
        outline: '0 !important',
        position: 'relative',
        webkitTransition: 'all 100ms',
        transition: 'all 100ms',
        boxSizing: 'border-box'
      }),
      indicatorSeparator: () => ({
        display: 'none'
      })
    }
    if (this.props.data.plans) {
      return (
       

        // floreplan list
        <div>
          <div style={{ display: this.props.floorplan ? 'block' : 'none' }} className="bg-[black]/80 bg-opacity-80 fixed z-[10501] overflow-hidden w-full h-full inset-0 top-0" id="close_modal" tabIndex="-1" role="dialog">
            <div className={` inset-0 mx-auto py-4 max-w-md   px-3 transition-all ${this.state.screenHeight<485 ? "":"sm:max-w-xl lg:max-w-2xl"} h-full w-full flex justify-center items-center`} role="document">
              <div className=" h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-2xl bg-clip-padding m-auto rounded-md rounded-none border-[none] inset-0">
                <div className="flex justify-between items-center mt-3  mx-3 ">
                  <div className="flex flex-col ">
                    <h1 className="text-white text-sm font-bold mt-0 mb-1">Floorplan</h1>
                    <p className="text-white text-xs font-normal leading-normal mt-0 mb-0">Select the floorplan document you would like to view.</p>
                  </div>
                  {/* Search Bar and icon */}
                  {/* <div className="hidden sm:inline-flex w-fit h-8 px-3 py-2 rounded border border-white justify-start items-center gap-28 ">
                    <input className="text-neutral-400 text-xs placeholder:text-xs font-normal leading-normal bg-transparent" type="text" placeholder="Search for floorplan" />
                  </div>
                  <div className="block sm:hidden bg-black bg-opacity-10 h-8 w-8 p-2 rounded-full flex justify-center items-center">
                  <svg className="w-6 h-6 fill-white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><g data-name="Layer 2"><g data-name="search"><rect  opacity="0"/><path d="M20.71 19.29l-3.4-3.39A7.92 7.92 0 0 0 19 11a8 8 0 1 0-8 8 7.92 7.92 0 0 0 4.9-1.69l3.39 3.4a1 1 0 0 0 1.42 0 1 1 0 0 0 0-1.42zM5 11a6 6 0 1 1 6 6 6 6 0 0 1-6-6z"/></g></g></svg>
                  </div> */}
                </div>
                <div className={`pr-1 mr-2 ml-3 h-44  grid grid-cols-3 gap-2 overflow-auto pdfListGrid mt-3 ${this.state.screenHeight<485 ? "":"sm:h-[17rem] lg:h-[19rem]"}`}>
                  {this.state.floorplan_list.map((elem, index) => (
                    <div className="flex flex-col h-fit rounded cursor-pointer" key={index} onClick={() => this.handleSelect(elem,index)}>
                      <div className={`w-full h-20  flex justify-center items-center text-white rounded-t-[inherit] sm:max-w-xl lg:max-w-2xl ${this.state.screenHeight<485 ? "":"sm:h-24 lg:h-28"}`}>
                        <img src={elem.url} className="h-full w-full object-cover rounded-t-[inherit]" /></div>
                      <div className={`w-full h-7 text-white flex justify-start items-center rounded-b-[inherit] whitespace-nowrap overflow-hidden  ${this.state.screenHeight<485 ? "":"sm:h-8 lg:h-9"} ${this.state.selectedOption===index?'bg-[#36f]':'bg-[#525252]'}`} >
                        <div className="mx-2 w-4 h-4 rounded-full cursor-pointer border flex justify-center items-center" >
                          {this.state.selectedOption === index ?
                            <div className="w-2 h-2 rounded-full bg-white"></div>
                            : ""}
                        </div>
                        <div className="text-xs cursor-pointer flex flex-1 items-center text-ellipsis overflow-hidden mr-2" >{elem.name}</div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className={`border-t border-neutral-500 mt-2  ${this.state.screenHeight<485 ? "":"sm:mt-1"} mx-3`}></div>
                <div className={`mx-3 mb-2  block pt-0 pb-1 border-t-[none] ${this.state.screenHeight<485 ? "":"sm:mb-3"}`}>
                  <center className={`flex justify-center mt-2  ${this.state.screenHeight<485 ? "":"sm:mt-3"}`}>
                    <button onClick={() => this.handleFloreplanViewOpen(true)} type="button" className="w-fit h-9  bg-[#36f] hover:bg-[#4572fc] border-0 rounded  m-0 px-3 text-xs  text-white font-semibold leading-6">View Floorplan</button>
                    <button  onClick={() => this.props.open_close('floorplan', false)} type="button" className="ml-3 w-fit h-9  rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent  m-0 px-3 text-xs text-white font-semibold leading-6">Cancel</button>

                  </center>

                </div>

              </div>
            </div>
          </div>

            {/* <div className="map_div" key={index} id={value.id} style={{ display: (value.index === 0 ? 'block' : 'none') }}> */}
            {/* <div className="map">

                    <img onMouseMove={(e)=>{this.MapAnnotate(e)}} alt="Floor" src={Resolvepath(value.url)} style={{ width: "100%", height: '240px' }} />
                  </div> */}
            {/* {value.pins.map((sub)=>{
              return(
                <div onClick={this.changesky} id={sub.id}  style={{top:(sub.y+8.5)+'%',left:(sub.x)+'%'}} className="box">
                </div>
                )

              })} */}
            {/* {
              this.props.annotate?

              <>
              {this.state.annotate?<div  style={{top:(this.state.annotate.top)+"px",left:(this.state.annotate.left+"px")}} className=" highlighter box">
            </div>:""}</>:""} */}

            {/* floreplan view */}
            <div style={{ display: (this.state.openFloreplanView ? 'block' : 'none') }} className="bg-[black]/80 bg-opacity-80 fixed z-[10501] overflow-hidden w-full h-screen inset-0 h-full w-full top-0" id="pdf_modal" tabIndex="-1" role="dialog">
            <div className="w-full h-full max-w-full m-0 p-0 flex justify-center items-center" role="document">
              <div className={` ${!this.state.isLandscape?"h-fit w-[95%] rounded":"h-full w-full "} sm:h-fit sm:max-w-lg lg:max-w-xl  sm:w-[95%] flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-2xl bg-clip-padding m-auto rounded-none sm:rounded-md  border-[none] inset-0`}>
                <div className="w-full h-full sm:p-1 box-border flex flex-col" >
                  <div className="h-12 sm:h-16 p-2 sm:px-0 w-full flex justify-between items-center">
                    <div className="w-fit sm:px-3 py-1 sm:bg-[#525252] text-sm text-white rounded-sm font-medium leading-normal">
                    {this.state.selectedImage.name}
                    </div>
                    <button onClick={() => this.handleFloreplanViewOpen(false)} type="button" className="bg-[#525252] backdrop-blur-2xl float-right shadow w-7 h-7 rounded-full flex justify-center items-center" data-dismiss="modal" aria-label="Close">
                      {/* <span style={{ marginLeft: '-50px' }} aria-hidden="true"> */}
                      <svg className="w-6 h-6" viewBox="0 0 32 32" fill="white" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.191 16L23.7534 9.43756C24.0823 9.10869 24.0823 8.5755 23.7534 8.24666C23.4245 7.91778 22.8913 7.91778 22.5625 8.24666L16 14.8091L9.43756 8.24666C9.10869 7.91778 8.5755 7.91778 8.24666 8.24666C7.91781 8.57553 7.91778 9.10872 8.24666 9.43756L14.8091 16L8.24666 22.5624C7.91778 22.8913 7.91778 23.4245 8.24666 23.7534C8.57553 24.0822 9.10872 24.0822 9.43756 23.7534L16 17.1909L22.5624 23.7534C22.8913 24.0822 23.4245 24.0822 23.7534 23.7534C24.0822 23.4245 24.0822 22.8913 23.7534 22.5624L17.191 16Z" fill="white" />
                      </svg>
                      {/* </span> */}
                    </button>
                  </div>
                  <div className="w-full h-full pt-1 sm:px-4 sm:mb-2 grow rounded">
                    <div className=" h-full rounded-t sm:rounded-b bg-white flex justify-center items-center ">
                      <img alt="Floor" src={this.state.selectedImage.url} className={`max-w-full  ${this.state.isLandscape?"h-full w-auto":"h-auto w-full "} sm:h-auto  sm:w-full rounded-[inherit]`} />
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* </div> */}
          </div>


        </div>
      )
    }
    else {
      return (
        <div style={{ display: this.props.floorplan ? 'block' : 'none' }} className="modal">
          <div className="modal-dialog" role="document">
            <div className="modal-content">
              <div className="modal-header" >
                <h5 style={{ color: "#222b45", margin: '0px' }} className="modal-title">FloorPlan does not exist!</h5>
                <button onClick={() => this.props.open_close('floorplan', false)} type="button" className="close" data-dismiss="modal" aria-label="Close">


                  <span aria-hidden="true">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                      <defs>
                        <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z" />
                      </defs>
                      <g fill="none" fillRule="evenodd" transform="translate(6 6)">
                        <use fill="#222B45" href="#prefix__close" />
                      </g>
                    </svg></span>
                </button>
              </div>
              <div className="modal-body">
                <p className="share_content">Floorplans are not available for this project</p>
              </div>
              <div style={{ display: "block" }} className="modal-footer">
                <center style={{ display: "flex", justifyContent: "center" }}>
                  <button onClick={() => this.props.open_close('floorplan', false)} type="button" className="btn cancel">Close</button>
                </center>
              </div>
            </div>
          </div>
        </div>
      )

    }
  }
}
export default Flooplan;
