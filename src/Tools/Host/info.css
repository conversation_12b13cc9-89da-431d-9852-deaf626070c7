.info-cards .info-card-item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    width: 100%;
    border-radius: 6px;
    box-shadow: 0 20px 40px -14px rgb(0 0 0 / 25%);
    overflow: hidden;
    transition: transform .5s;
    -webkit-transition: transform .5s;
    z-index: 1;
}

.info-cards .info-card-image {
    height: 200px;
    overflow: hidden;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 6px 6px 0 0;
    opacity: .91;
}

.info-cards .card-info {
    display: flex;
    flex: 1 1 auto;
    flex-direction: column;
    padding: 10px;
    background: #fff;
    height: fit-content;
}
.card-intro {
    margin: 0;
    padding: 10px 0;
    max-height: 150px;
    overflow: auto;
}

.info-url {
    background-color: #2979ff;
    border: solid;
    color: #fff;
    border-radius: 13px;
    padding: 8px 10px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 13px;
    width: 100px;
}

@media (min-width: 56rem){
	.info-cards {
		width: 400px;
	}
}


@media (min-width: 40rem){
	.info-cards {
		width: 400px;
	}
}

.info-cards {
    display: flex;
    padding: 1rem;
    margin-bottom: 2rem;
    width: 400px;
}

.info-card-image model-viewer{
	width: auto !important;
	height: inherit !important;
}