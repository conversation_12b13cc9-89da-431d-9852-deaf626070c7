import React from 'react';
import {Resolvepath} from "../../components/Resolvepath"
function loadCustomIcon(props,customicon){

    let assets = null
    if(customicon.length)
    {
        assets = customicon.map((value,index) => { 
        return(
            <img crossOrigin="anonymous" id={value} onLoad={props.sceneloader} src={Resolvepath(value)} key={index}/>
        )
        });
    }
    return assets
}

function AssetsLoader(props){
    // Load Assets  
    let render=[];    
    let customIcon = [];

    for (var key in props.data){
   
        // Loading Custom Icons
        for (var link in props.data[key].links)
        {
            if(props.data[key].links[link].customicon)
            {
                if(!customIcon.includes(props.data[key].links[link].customicon))
                {
                    customIcon.push(props.data[key].links[link].customicon)
                }
            }
        }
        //-
        render.push({
            url:props.data[key].url,
            index:key,
            name:props.data[key].name,
            thumbnail:props.data[key].thumbnail
        })
    }  
    function loaded (image){
        props.loadedlisten(image)
        props.sceneloader()
    }
    let assets =render.map((value) => {
        return(
        <img crossOrigin="anonymous" id={value.index+"__thumb"}  onLoad={()=>{loaded(value.index)}} src={Resolvepath(value.thumbnail)} alt={value.name} key={value.index}/>
    )});

    
    // Load Hotspot Icon
    let CustomIcon = loadCustomIcon(props,customIcon)

    let hotspotIcon = <img 
        id="hotspot" 
        crossOrigin="anonymous"
        src={Resolvepath(props.hotspotIcon.url)}
        alt="Hotspot"/>
    
    return(
        <a-assets>
            {assets}
            {hotspotIcon}
            {CustomIcon}
        </a-assets>
    )
    }

export default AssetsLoader;
