import React from "react";
import { connect } from "react-redux";
import * as HostActions from "../../Actions/HostAction";
import EndSessionApi from "../helpers/BackendApis";

class Timer extends React.Component {

    constructor(props) {
        super(props)
        this.state = {
            start: new Date(this.props.start),
            timer: 0,
            sleepmodal: false,
            sleeptimer: 0,
            timeoutmodal: false
        }
        this.sleeptime = this.props.sleeptime || 10; //min
        this.maxsessionTime = this.props.maxsessionTime || 60; //min
        this.sleepjob = false;
        this.StartSleep = this.StartSleep.bind(this);
        this.StopSleep = this.StopSleep.bind(this);
        this.EndSession = this.EndSession.bind(this);
    }
    componentDidMount() {
        if (this.props.trackInactivity) {
            this.onInactive((this.sleeptime * 60 * 1000), () => {
                if (!this.state.timeoutmodal) {
                    this.setState({
                        sleepmodal: true,
                    })
                }

                this.StartSleep()

            })
        }


        const job = setInterval(() => {
            const time = new Date().getTime();
            const starttime = this.state.start.getTime();
            const diff = time - starttime;
            if (this.props.trackMaxtimeout) {
                if (diff >= ((((this.maxsessionTime * 60) - 10) * 1000))) {
                    this.StopSleep()
                    this.setState({
                        timeoutmodal: true,
                        sleeptimer: 10,
                        sleepmodal: false
                    })

                    this.StartSleep()
                    clearInterval(job)
                }
            }
            this.setState({ timer: diff })
        }, 1000);
    }
    convertTimetohours(diff) {
        let seconds = diff / 1000;
        let minutes = seconds / 60;
        let hours = minutes / 60;

        let tallyseconds = parseInt(seconds % 60);
        let tallyminutes = parseInt(minutes);
        if (tallyminutes <= 9)
            tallyminutes = "0" + tallyminutes
        if (tallyseconds <= 9)
            tallyseconds = "0" + tallyseconds
        if (hours > 0) {
            return (tallyminutes + ":" + tallyseconds)
        }
        return (tallyminutes + ":" + tallyseconds)
    }
    onInactive(ms, cb) {

        var wait = setTimeout(cb, ms);
        var iframe = document.getElementById('showcase-iframe');
        if (iframe) {
            iframe.contentWindow.onmousemove = iframe.contentWindow.mousedown = iframe.contentWindow.mouseup = iframe.contentWindow.onkeydown = iframe.contentWindow.onkeyup = iframe.contentWindow.focus = function () {


                clearTimeout(wait);
                wait = setTimeout(cb, ms);
            };

        }
        document.onmousemove = document.mousedown = document.mouseup = document.onkeydown = document.onkeyup = document.focus = function () {
            clearTimeout(wait);
            wait = setTimeout(cb, ms);

        };
    }
    StartSleep() {
        if (!this.state.timeoutmodal) {
            this.setState({
                sleeptimer: 10,
            })
        }

        this.sleepjob = setInterval(() => {
            if (this.state.sleeptimer <= 0) {
                this.EndSession()
            } else {
                this.setState({ sleeptimer: this.state.sleeptimer - 1 })
            }
        }, 1000);
    }
    StopSleep() {
        clearInterval(this.sleepjob);
        this.setState({ sleepmodal: false })
    }
    EndSession() {
        if (this.props.SessionDetails.type === "pixel_streaming") {
            if (this.props.trackMaxtimeout) {
                EndSessionApi(this.props.roomId)
            }
        }
        return
    }
    render() {
        return (
            <>
                <div
                    style={{
                        display: this.props.ShowControls ? "inline-flex" : "none",
                        position: this.props.position || 'relative',
                        border: this.props.style?.border || 'none'
                    }}
                    className="items-center justify-center bg-[#F3F4F6] text-black h-[25px] w-[56px] rounded-[34px]"
                >
                    <span className="text-[12px] font-medium text-center leading-[150%]">
                        {this.state.timer < 0 ? "00:00" : this.convertTimetohours(this.state.timer)}
                    </span>
                </div>

                {/* <div style={{display:this.state.timeoutmodal===true || this.state.sleepmodal===true?'block':'none', border:"1px solid red"}} className="fixed left-0 top-0  bg-[black]/30 bg-opacity-40 backdrop-blur fixed z-[9000] hidden overflow-hidden inset-0 p-4 h-[100vh] w-[100vw]" >
                    <div className="absolute inset-0 mx-3 sm:mx-auto flex justify-center items-center sm:my-7" role="document">
                    <div className="absolute max-w-full p-4 sm:max-w-lg h-fit flex flex-col w-full pointer-events-auto bg-transparent backdrop-blur-3xl m-auto rounded inset-0 sm:shadow">
                        {this.state.timeoutmodal?
                        <><div className="flex items-start justify-between  rounded-t-[0.3rem] mb-3">
                        <h5 className="text-lg font-[bold] not-italic tracking-[normal] text-white mb-0 font-sans mt-0">Session limit has reached. Kindly schedule another session to continue</h5>
                        </div>
                        <div className="relative flex-auto">
                        <p className="text-sm mt-0 mb-3 mx-0 text-white ">Session ending in {this.state.sleeptimer} seconds</p>
                        </div>
                        
                        </>:<></>} */}
                {/* {this.state.sleeptimer==true?<>
                    <div className="flex items-start justify-between  rounded-t-[0.3rem] mb-3">
                    <h5 className="text-lg font-[bold] not-italic tracking-[normal] text-white mb-0 font-sans mt-0">No activities were not found for {this.sleeptime} minutes</h5>

                    </div>
                    <div className="relative flex-auto">
                    <p className="text-sm mt-0 mb-3 mx-0 text-white ">This session will get ends in {this.state.sleeptimer} seconds</p>
                    </div>
                    <div  className=" block border-t-[none]">
                    <center className="flex justify-center mt-2 sm:mt-3">
                        <button onClick={() => { this.EndSession() } } type="button" className="w-40 h-10 lg:h-11 rounded border-2 border-solid border-[#36f] hover:border-[#4572fc] bg-transparent m-0 px-3 text-sm text-white font-semibold leading-6">End Now</button>
                        <button onClick={()=>{ this.StopSleep()}} type="button" className="ml-3 w-40 h-10 lg:h-11  rounded bg-[#36f] hover:bg-[#4572fc] border-0 m-0 px-3 text-sm text-white font-semibold leading-6">Cancel</button>
                    </center>
                    </div>
                    </>:<></>} */}
                {/* </div>
                    </div>
                </div> */}
            </>
        )
    }
}
const mapStateToProps = state => {
    return {
        ShowControls: state.Call.ShowControls,
    }
}

const mapDispatchTothisprops = {
    ...HostActions,
}

export default connect(mapStateToProps, mapDispatchTothisprops)(Timer)