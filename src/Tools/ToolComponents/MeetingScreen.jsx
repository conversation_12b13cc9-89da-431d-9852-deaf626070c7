import React from 'react';
import * as HostActions from "../../Actions/HostAction";
import { connect } from 'react-redux';
import * as ExceptionActions from "../../Actions/Exception"
import * as Sessions from '../../Actions/Sessions';
import * as AuthException from "../../Actions/Auth"


class MeetingScreen extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      tooltip:false,
      isVideoOn: false,
      isMicOn: false,
      url: ''
    };

    
    this.timeoutId = null;
    this.handlecopy = this.handlecopy.bind(this);
    this.toggleVideo=this.toggleVideo.bind(this);
  }
componentDidUpdate(prevProps, prevState) {
  const LocalVideo = document.getElementById("LocalVideo");
  const CanvasVideo = document.getElementById("CanvasVideo");

  if (this.state.isVideoOn !== prevState.isVideoOn && this.props.LocalStream) {
    if (this.state.isVideoOn) {
      LocalVideo.srcObject = this.props.LocalStream;
    } else {
      CanvasVideo.srcObject = this.props.LocalStream;
    }
  }

  // Sync local state with Redux state when props change
  if (prevProps.Video !== this.props.Video) {
    this.setState({ isVideoOn: this.props.Video });
  }
  if (prevProps.Audio !== this.props.Audio) {
    this.setState({ isMicOn: this.props.Audio });
  }
}
componentDidMount() {
  // Ensure socket connection is established for toggle functions
  if (this.props.Auth) {
    this.props.ConnecToWebSocket(this.props.roomId, this.props.Auth?.first_name || this.props.Auth?.email || "Host");
  }

  // Initialize local state based on Redux state
  this.setState({
    isVideoOn: this.props.Video,
    isMicOn: this.props.Audio,
    url: window.location.protocol + '//' + window.location.hostname +
         (window.location.port.length ? ":" + window.location.port : "") +
         "/salestool/joinroom/" + this.props.roomId,
  }, () => {
    const LocalVideo = document.getElementById("LocalVideo");
    const CanvasVideo = document.getElementById("CanvasVideo");
    
    if(!this.state.isMicOn){
      console.log("dummy audio added")
      // this.props.LocalStream.getAudioTracks()[0].enabled=false;
      this.props.SetLocalStream(this.props.DummyAudio);
    }

    if (this.state.isVideoOn) {
      LocalVideo.srcObject = this.props.LocalStream;
    } else {
      this.props.LocalStream.removeTrack(this.props.LocalStream.getVideoTracks()[0])
      this.props.LocalStream.addTrack(HostActions.canvastrack);
      CanvasVideo.srcObject = this.props.LocalStream;
      console.log("canvas added initially",this.props.LocalStream.getVideoTracks())
    }
  });
}
toggleVideo = () => {
  // Use the same pattern as NewRealTimeController - proper toggle with socket broadcasting
  if (this.props.CameraAccess) {
    this.props.ToggleUserVideo({
      roomId: this.props.roomId,
      Peers: this.props.Peers,
      Audio: this.props.Audio,
      Video: this.props.Video,
      Screen: this.props.ScreenShare,
      LocalStream: this.props.LocalStream
    });
    // Local state will be synced via componentDidUpdate
  } else {
    this.props.SetModalException('Camera Access is not allowed');
  }
};
  toggleMic = () => {
    // Use the same pattern as NewRealTimeController - proper toggle with socket broadcasting
    if (this.props.MicrophoneAccess) {
      this.props.ToggleUserAudio({
        roomId: this.props.roomId,
        Peers: this.props.Peers,
        Audio: this.props.Audio,
        Video: this.props.Video,
        Screen: this.props.ScreenShare,
        LocalStream: this.props.LocalStream
      });
      // Local state will be synced via componentDidUpdate
    } else {
      this.props.SetModalException('Microphone Access is not allowed');
    }

  };
  handlecopy = ()=>{
    navigator.clipboard.writeText(this.state.shortUrl || encodeURI(this.state.url));
    this.setState({
      tooltip:true
    })
    this.timeoutId = setTimeout(() => {
        this.setState({
      tooltip:false
    });
    }, 2000); // 3 seconds
  }
  routeToSalesTool  =()=>{
  localStorage.setItem('isVideoOn', String(this.state.isVideoOn));
  localStorage.setItem('isMicOn', String(this.state.isMicOn));
    this.props.handleClose(false)
    this.setState({
      tooltip:false
    })
  }
  handleShare = ()=>{
  if (navigator.share) {
      navigator.share({
        title: 'Join my meeting',
        text: 'Click the link to join my meeting',
        url: this.state.url,
      })
      .then(() => console.log('Successful share'))
      .catch((error) => console.log('Error sharing', error));
    } else {
      this.handlecopy();
    }
  }

  handleLeaveSession = () =>{
     window.location.href = `/sessions`
  }

  render() {
    return (
 <div className="w-screen h-screen flex flex-col items-center select-none bg-gray-50">
          <div className="w-full flex justify-start py-2.5 border-b-2 pl-3 bg-white">
            <span
               onClick={this.handleLeaveSession}
              className="w-8 bg-gray-100 p-2.5 rounded-md flex justify-center cursor-pointer"
              >
              <svg width="6" height="14" viewBox="0 0 6 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path id="Back" d="M0 7.0039C0 7.16006 0.0446808 7.29279 0.140426 7.4021L5.24681 13.3438C5.32979 13.4453 5.4383 13.5 5.55958 13.5C5.80851 13.5 6 13.2736 6 12.9613C6 12.8129 5.94894 12.6802 5.87872 12.5865L1.07872 7.0039L3.47872 4.21261L5.87872 1.42132C5.94894 1.31982 6 1.18709 6 1.03874C6 0.734234 5.80851 0.5 5.55958 0.5C5.4383 0.5 5.32979 0.554655 5.24681 0.656156L0.140426 6.60571C0.0446808 6.71501 0 6.84775 0 7.0039Z" fill="black"/>
              </svg>
            </span>
          </div>

          <div className="w-full sm:w-[75%] md:w-[50%] lg:w-[35%] xl:w-[30%] flex flex-col gap-4 p-3 justify-between bg-gray-50">

            <div className="w-full flex flex-col justify-center items-center gap-3">
              <div className="rounded-lg w-full  border aspect-video relative flex items-center justify-center">
                  <video
                    id="LocalVideo"
                    className={`absolute inset-0 !aspect-video rounded-md object-cover ${ this.state.isVideoOn ? 'opacity-100 z-10' : 'opacity-0 z-0'}`}
                    autoPlay
                    playsInline
                    muted 
                  />
                  <video
                    id="CanvasVideo"
                    className={`absolute inset-0 !aspect-video rounded-md object-cover ${this.state.isVideoOn ? 'opacity-0 z-0' : 'opacity-100 z-10'}`}
                    autoPlay
                    playsInline
                    muted 
                  />
                <div className="absolute bottom-8 z-20 bg-black/60 w-fit px-2 h-8 flex justify-center items-center rounded-md">
                  <p className="text-white text-sm font-medium mb-0">
                   {this.props.userId.displayName ? this.props.userId.displayName : this.props.Auth?.first_name ? this.props.Auth?.first_name : "Host"}
                  </p>
                </div>
              </div>

              <div className="flex gap-4">
                <button onClick={this.toggleVideo} className={`w-10 h-10 flex justify-center items-center rounded-md cursor-pointer ${this.state.isVideoOn ? 'bg-gray-200' : 'bg-red-100'}`}>
                {this.state.isVideoOn ? (
                  <svg width="13" height="15" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clipPath="url(#clip0_3240_6427)">
                      <g>
                        <path d="M7.1 2.33643H1.7C1.03726 2.33643 0.5 3.008 0.5 3.83643V11.3364C0.5 12.1649 1.03726 12.8364 1.7 12.8364H7.1C7.76274 12.8364 8.29999 12.1649 8.29999 11.3364V3.83643C8.29999 3.008 7.76274 2.33643 7.1 2.33643Z" fill="#111928" />
                        <path d="M12.2 3.31143C12.1091 3.24491 12.0058 3.20948 11.9005 3.20869C11.7952 3.2079 11.6915 3.24178 11.6 3.30693L9.49999 4.79943V10.4492L11.579 12.0992C11.67 12.1712 11.7747 12.2113 11.8824 12.2152C11.99 12.2192 12.0965 12.1869 12.1907 12.1217C12.2848 12.0565 12.3633 11.9608 12.4177 11.8447C12.4721 11.7285 12.5005 11.5963 12.5 11.4617V3.96168C12.5001 3.8299 12.4724 3.70041 12.4198 3.58626C12.3671 3.47211 12.2913 3.37731 12.2 3.31143Z" fill="#111928" />
                      </g>
                    </g>
                    <defs>
                      <clipPath id="clip0_3240_6427">
                        <rect width="12" height="14" fill="white" transform="translate(0.5 0.586426)" />
                      </clipPath>
                    </defs>
                  </svg>
                ):(
                  <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g>
                      <path fillRule="evenodd" clipRule="evenodd" d="M0.46967 15.4697C0.176777 15.7626 0.176777 16.2374 0.46967 16.5303C0.762563 16.8232 1.23744 16.8232 1.53033 16.5303L4.18575 13.8749H9.90001C10.618 13.8749 11.2 13.1554 11.2 12.2678V6.86065L12.5 5.56065V11.3171L14.7523 13.085C14.8508 13.1621 14.9643 13.2051 15.0809 13.2093C15.1975 13.2136 15.3129 13.1789 15.4149 13.1091C15.5169 13.0392 15.6019 12.9367 15.6609 12.8123C15.7198 12.6879 15.7506 12.5462 15.75 12.402V4.36625C15.7501 4.22506 15.7201 4.08632 15.6631 3.96402C15.606 3.84171 15.5239 3.74014 15.425 3.66955C15.3265 3.59829 15.2146 3.56032 15.1005 3.55948C14.9864 3.55863 14.8742 3.59493 14.775 3.66473L13.4991 4.5616L16.5303 1.53033C16.8232 1.23744 16.8232 0.762563 16.5303 0.46967C16.2374 0.176777 15.7626 0.176777 15.4697 0.46967L11.2 4.73933V4.23205C11.2 3.34445 10.618 2.62491 9.90001 2.62491H4.05002C3.33205 2.62491 2.75002 3.34445 2.75002 4.23205V12.2678C2.75002 12.5447 2.80669 12.8054 2.90649 13.0328L0.46967 15.4697Z" fill="#C81E1E" />
                    </g>
                  </svg>
                )}

                </button>
                <button onClick={this.toggleMic} className={`w-10 h-10 flex justify-center items-center rounded-md cursor-pointer ${this.state.isMicOn ? 'bg-gray-200' : 'bg-red-100'}`}>
                  {this.state.isMicOn?(<svg width="13" height="15" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g id="microphone" clip-path="url(#clip0_3240_6429)">
                  <g id="Vector">
                  <path d="M10.875 4.78643C10.7092 4.78643 10.5503 4.86018 10.4331 4.99145C10.3158 5.12273 10.25 5.30077 10.25 5.48643V7.58643C10.25 8.32903 9.98661 9.04122 9.51777 9.56633C9.04893 10.0914 8.41304 10.3864 7.75 10.3864H5.25C4.58696 10.3864 3.95107 10.0914 3.48223 9.56633C3.01339 9.04122 2.75 8.32903 2.75 7.58643V5.48643C2.75 5.30077 2.68415 5.12273 2.56694 4.99145C2.44973 4.86018 2.29076 4.78643 2.125 4.78643C1.95924 4.78643 1.80027 4.86018 1.68306 4.99145C1.56585 5.12273 1.5 5.30077 1.5 5.48643V7.58643C1.50099 8.69999 1.8964 9.76763 2.59945 10.555C3.30249 11.3425 4.25574 11.7853 5.25 11.7864H5.875V13.1864H4.625C4.45924 13.1864 4.30027 13.2602 4.18306 13.3914C4.06585 13.5227 4 13.7008 4 13.8864C4 14.0721 4.06585 14.2501 4.18306 14.3814C4.30027 14.5127 4.45924 14.5864 4.625 14.5864H8.375C8.54076 14.5864 8.69973 14.5127 8.81694 14.3814C8.93415 14.2501 9 14.0721 9 13.8864C9 13.7008 8.93415 13.5227 8.81694 13.3914C8.69973 13.2602 8.54076 13.1864 8.375 13.1864H7.125V11.7864H7.75C8.74426 11.7853 9.69751 11.3425 10.4006 10.555C11.1036 9.76763 11.499 8.69999 11.5 7.58643V5.48643C11.5 5.30077 11.4342 5.12273 11.3169 4.99145C11.1997 4.86018 11.0408 4.78643 10.875 4.78643Z" fill="#111928"/>
                  <path d="M7.125 0.586426H5.875C4.49429 0.586426 3.375 1.84003 3.375 3.38643V6.88643C3.375 8.43282 4.49429 9.68643 5.875 9.68643H7.125C8.50571 9.68643 9.625 8.43282 9.625 6.88643V3.38643C9.625 1.84003 8.50571 0.586426 7.125 0.586426Z" fill="#111928"/>
                  </g>
                  </g>
                  <defs>
                  <clipPath id="clip0_3240_6429">
                  <rect width="12" height="14" fill="white" transform="translate(0.5 0.586426)"/>
                  </clipPath>
                  </defs>
                  </svg>):(<svg width="17" height="18" viewBox="0 0 17 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g id="mic">
                  <path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M15.4846 1.35007C15.7281 1.01496 15.6538 0.545937 15.3187 0.302469C14.9836 0.059002 14.5145 0.133288 14.2711 0.468393L11.7622 3.92149C11.292 3.17267 10.4367 2.67188 9.45948 2.67188H8.11604C6.63212 2.67188 5.42916 3.82671 5.42916 5.25128V8.47553C5.42916 9.44655 5.98806 10.2922 6.81389 10.7323L6.29206 11.4506C6.01709 11.3253 5.76374 11.1549 5.54441 10.9443C5.04052 10.4606 4.75744 9.80448 4.75744 9.12038V7.18583C4.75744 7.01481 4.68667 6.85079 4.5607 6.72985C4.43473 6.60892 4.26387 6.54098 4.08572 6.54098C3.90757 6.54098 3.73672 6.60892 3.61075 6.72985C3.48477 6.85079 3.414 7.01481 3.414 7.18583V9.12038C3.41507 10.1462 3.84003 11.1297 4.59563 11.8551C4.8708 12.1193 5.18162 12.3413 5.51725 12.517L2.51536 16.6487C2.27189 16.9838 2.34618 17.4529 2.68128 17.6963C3.01639 17.9398 3.48541 17.8655 3.72888 17.5304L7.04233 12.9698C7.17532 12.9827 7.30947 12.9894 7.44432 12.9895H8.11604V14.2792H6.7726C6.59445 14.2792 6.42359 14.3471 6.29762 14.4681C6.17165 14.589 6.10088 14.753 6.10088 14.924C6.10088 15.0951 6.17165 15.2591 6.29762 15.38C6.42359 15.501 6.59445 15.5689 6.7726 15.5689H10.8029C10.9811 15.5689 11.1519 15.501 11.2779 15.38C11.4039 15.2591 11.4746 15.0951 11.4746 14.924C11.4746 14.753 11.4039 14.589 11.2779 14.4681C11.1519 14.3471 10.9811 14.2792 10.8029 14.2792H9.45948V12.9895H10.1312C11.1998 12.9885 12.2243 12.5805 12.9799 11.8551C13.7355 11.1297 14.1604 10.1462 14.1615 9.12038V7.18583C14.1615 7.01481 14.0907 6.85079 13.9648 6.72985C13.8388 6.60892 13.6679 6.54098 13.4898 6.54098C13.3116 6.54098 13.1408 6.60892 13.0148 6.72985C12.8888 6.85079 12.8181 7.01481 12.8181 7.18583V9.12038C12.8181 9.80448 12.535 10.4606 12.0311 10.9443C11.5272 11.428 10.8438 11.6998 10.1312 11.6998H7.96507L8.43358 11.0549H9.45948C10.9434 11.0549 12.1464 9.9001 12.1464 8.47553V5.94474L15.4846 1.35007Z" fill="#C81E1E"/>
                  </g>
                  </svg>)}
                </button>
              </div>
            </div>

            <div className="flex flex-col items-center gap-2 w-full relative">
              <p className="text-[#6B7280] text-[14px] font-medium">Meeting Link</p>

              <div className="bg-white  relative w-full max-w-md flex justify-between items-center h-12 rounded-md px-3 overflow-hidden">
              <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g id="link" clip-path="url(#clip0_3240_6435)">
              <path id="Vector" d="M4.42143 15.5864L4.41901 15.5864C3.78606 15.5875 3.16296 15.3837 2.62855 14.9946C2.09349 14.6051 1.67024 14.0462 1.41888 13.3841C1.16745 12.7218 1.10133 11.9907 1.23036 11.2852C1.35938 10.5797 1.67653 9.93743 2.13566 9.43773L2.13608 9.43727L5.11986 6.18175C5.11995 6.18166 5.12003 6.18157 5.12011 6.18147C5.73438 5.51275 6.55678 5.14644 7.40396 5.14644C8.2482 5.14644 9.06784 5.51021 9.68141 6.17453C9.73763 6.24259 9.77362 6.33906 9.77204 6.44557C9.77041 6.55477 9.72967 6.65129 9.66979 6.7166C9.61114 6.78056 9.54144 6.80811 9.47853 6.80917C9.41708 6.81021 9.34913 6.78624 9.29043 6.72801C8.79646 6.1942 8.1196 5.88549 7.40396 5.88549C6.68521 5.88549 6.00558 6.19689 5.51106 6.73499L5.51056 6.73553L2.52645 9.99229C2.03369 10.5301 1.76352 11.2508 1.76352 11.9941C1.76352 12.3631 1.83006 12.7293 1.9602 13.0721C2.09035 13.4151 2.28211 13.7293 2.52645 13.996C2.77087 14.2627 3.06343 14.4769 3.3887 14.624C3.71411 14.7711 4.06464 14.8475 4.41985 14.8475C5.13916 14.8475 5.81915 14.5352 6.31325 13.996L6.69943 13.5745C6.69946 13.5745 6.69949 13.5745 6.69953 13.5744C6.72952 13.5418 6.7627 13.5186 6.79592 13.5036C6.82904 13.4887 6.86277 13.4818 6.89563 13.4819C6.92848 13.4819 6.96222 13.489 6.99534 13.504C7.0286 13.5191 7.06179 13.5426 7.09174 13.5754C7.12177 13.6083 7.14763 13.6496 7.16592 13.6981C7.18421 13.7465 7.19402 13.7997 7.19394 13.8543C7.19385 13.9089 7.18386 13.9621 7.16543 14.0104L7.63259 14.1886L7.16542 14.0104C7.147 14.0587 7.12103 14.0999 7.09093 14.1327L7.09034 14.1333L6.70375 14.5552C6.70369 14.5553 6.70363 14.5553 6.70357 14.5554C6.39965 14.8867 6.04116 15.1466 5.64997 15.3226C5.25886 15.4986 4.8415 15.5877 4.42143 15.5864ZM8.60116 11.0276L8.6001 11.0276C8.17977 11.0284 7.76224 10.9385 7.37107 10.7619C6.98118 10.5859 6.62389 10.3265 6.32081 9.99619C6.26222 9.92866 6.22388 9.83082 6.22475 9.72187C6.22562 9.61156 6.26649 9.51381 6.32695 9.44782C6.38613 9.38323 6.45657 9.35574 6.52001 9.35514C6.58338 9.35454 6.65376 9.38062 6.71347 9.44356L6.71346 9.44357L6.71548 9.44567C7.21584 9.96696 7.89024 10.2662 8.60096 10.2662C9.31168 10.2662 9.98608 9.96696 10.4864 9.44567L10.4865 9.44577L10.4944 9.43722L13.4772 6.18187C13.4773 6.18176 13.4774 6.18164 13.4775 6.18153C13.7222 5.91513 13.9143 5.60099 14.0447 5.25809C14.1752 4.91507 14.2419 4.54865 14.2419 4.17949C14.2419 3.81032 14.1752 3.4439 14.0447 3.10088C13.9143 2.75784 13.7221 2.44357 13.4772 2.1771L13.4773 2.17702L13.4699 2.16934C12.9698 1.64783 12.2954 1.34842 11.5847 1.34842C10.8739 1.34842 10.1996 1.64783 9.69944 2.16935L9.69935 2.16926L9.69167 2.17764L9.09688 2.82677C9.037 2.89208 8.96564 2.91946 8.90151 2.91943C8.83738 2.91941 8.76602 2.89197 8.70616 2.82659C8.64496 2.75976 8.60394 2.66047 8.60398 2.54878C8.60401 2.4371 8.6451 2.33786 8.70632 2.27109L8.70644 2.27096L9.30122 1.62184C9.30127 1.62179 9.30131 1.62174 9.30135 1.6217C9.91503 0.952525 10.7373 0.586078 11.5843 0.586426C12.4313 0.586774 13.2534 0.953949 13.8667 1.62374C14.4813 2.29499 14.833 3.21385 14.8327 4.17998C14.8323 5.14611 14.48 6.06467 13.8649 6.73539L13.8648 6.73548L10.8807 9.99137L10.8803 9.99182C10.577 10.3235 10.2191 10.5841 9.82843 10.7609C9.43788 10.9376 9.02096 11.0278 8.60116 11.0276Z" fill="#6B7280" stroke="#6B7280"/>
              </g>
              <defs>
              <clipPath id="clip0_3240_6435">
              <rect width="16" height="16" fill="white" transform="translate(0 0.0864258)"/>
              </clipPath>
              </defs>
              </svg>
                <p className="truncate px-2 text-gray-900 text-sm font-normal mb-0">{this.state.url}</p>
              <button
                onClick={this.handlecopy}
                className="flex-shrink-0 text-gray-500 hover:text-gray-700"
                title="Copy link to clipboard"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
              </button>
                {this.state.tooltip && (
                <div className="absolute top-[-10px] right-0 mt-2 text-gray-500 text-xs !bg-white text-center">
                  Copied
                  </div>
                )}
              </div>

              <div className="w-full flex flex-row justify-center sm:justify-between gap-4">
                <button onClick={this.handleShare} className="px-2 py-2 flex items-center min-h-10 cursor-pointer gap-2 bg-white rounded-md hover:!bg-gray-100">
                <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g id="share-nodes-outline">
                <path id="Vector" d="M10.1497 8.01299C9.86979 8.01324 9.59275 8.07138 9.33504 8.18395C9.07734 8.29652 8.84424 8.46122 8.64956 8.6683L5.90843 7.19092C5.93514 7.05489 5.9488 6.91649 5.94924 6.77772C5.94582 6.69599 5.93781 6.61453 5.92524 6.53375L8.54635 5.37754C8.86423 5.7626 9.30375 6.02008 9.78773 6.10476C10.2717 6.18944 10.7692 6.09592 11.1929 5.84061C11.6165 5.58529 11.9392 5.18452 12.1043 4.70867C12.2694 4.23281 12.2664 3.71229 12.0957 3.23853C11.925 2.76476 11.5976 2.36803 11.171 2.118C10.7444 1.86798 10.2458 1.78064 9.76289 1.87134C9.27994 1.96203 8.84347 2.22495 8.53013 2.61393C8.2168 3.00292 8.04662 3.49309 8.04949 3.99837C8.05285 4.08113 8.06086 4.16362 8.07349 4.24542L5.45298 5.39854C5.18118 5.06397 4.81721 4.82207 4.40911 4.70476C4.001 4.58745 3.56793 4.60024 3.16703 4.74143C2.76613 4.88263 2.41624 5.14561 2.16352 5.49567C1.91079 5.84572 1.76711 6.26642 1.75144 6.70224C1.73576 7.13806 1.84881 7.56854 2.07569 7.93688C2.30257 8.30521 2.63261 8.5941 3.02226 8.76543C3.41192 8.93676 3.84289 8.98248 4.2583 8.89655C4.67372 8.81062 5.05407 8.59709 5.34917 8.28413L8.0891 9.76151C8.0628 9.89758 8.04953 10.036 8.04949 10.1747C8.04949 10.6023 8.17267 11.0202 8.40345 11.3757C8.63423 11.7312 8.96224 12.0083 9.34601 12.1719C9.72978 12.3355 10.1521 12.3783 10.5595 12.2949C10.9669 12.2115 11.3411 12.0056 11.6348 11.7033C11.9286 11.401 12.1286 11.0158 12.2096 10.5964C12.2907 10.1771 12.2491 9.74246 12.0901 9.34745C11.9312 8.95245 11.662 8.61484 11.3166 8.3773C10.9712 8.13977 10.5651 8.01299 10.1497 8.01299ZM10.1497 3.07192C10.3278 3.07192 10.5018 3.12625 10.6498 3.22805C10.7978 3.32985 10.9132 3.47454 10.9813 3.64383C11.0495 3.81312 11.0673 3.9994 11.0326 4.17911C10.9978 4.35882 10.9121 4.5239 10.7862 4.65347C10.6603 4.78303 10.5 4.87127 10.3253 4.90702C10.1507 4.94276 9.96976 4.92442 9.80529 4.8543C9.64082 4.78418 9.50024 4.66543 9.40133 4.51308C9.30243 4.36072 9.24964 4.1816 9.24964 3.99837C9.24964 3.75266 9.34447 3.51701 9.51327 3.34327C9.68208 3.16953 9.91102 3.07192 10.1497 3.07192ZM3.84899 7.70417C3.67096 7.70417 3.49693 7.64984 3.34891 7.54804C3.20089 7.44624 3.08552 7.30154 3.01739 7.13226C2.94927 6.96297 2.93144 6.77669 2.96617 6.59698C3.0009 6.41726 3.08663 6.25219 3.21251 6.12262C3.3384 5.99305 3.49878 5.90482 3.67338 5.86907C3.84799 5.83332 4.02897 5.85167 4.19344 5.92179C4.35792 5.99191 4.49849 6.11066 4.5974 6.26301C4.6963 6.41537 4.74909 6.59449 4.74909 6.77772C4.74804 6.92341 4.71327 7.06675 4.64768 7.19586C4.64768 7.19956 4.64228 7.20142 4.64048 7.2045V7.21439C4.56384 7.362 4.44978 7.48548 4.31041 7.57172C4.17105 7.65796 4.0116 7.70373 3.84899 7.70417ZM10.1497 11.1012C9.91102 11.1012 9.68208 11.0035 9.51327 10.8298C9.34447 10.6561 9.24964 10.4204 9.24964 10.1747C9.25053 10.0309 9.28446 9.88928 9.34865 9.76151C9.34865 9.75657 9.35525 9.75286 9.35825 9.74792V9.73804C9.43336 9.59338 9.54443 9.47184 9.68012 9.38584C9.81581 9.29984 9.97125 9.25245 10.1306 9.24852C10.2899 9.2446 10.4473 9.28427 10.5869 9.36349C10.7264 9.44272 10.843 9.55864 10.9247 9.69943C11.0065 9.84022 11.0505 10.0008 11.0522 10.1648C11.0539 10.3288 11.0132 10.4904 10.9344 10.6329C10.8556 10.7755 10.7414 10.8939 10.6036 10.9762C10.4657 11.0585 10.3091 11.1016 10.1497 11.1012Z" fill="#111928"/>
                </g>
                </svg>
                  <p className="text-gray-900 text-sm font-medium mb-0">Share the link</p>
                </button>

                <button onClick={this.routeToSalesTool} className="px-2 py-2 flex items-center cursor-pointer gap-2 bg-blue-600 rounded-md text-white hover:bg-blue-700">
                  <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g id="videocamera">
                  <g id="Vector">
                  <path d="M7.69999 1.83643H1.4C0.626801 1.83643 0 2.508 0 3.33643V10.8364C0 11.6649 0.626801 12.3364 1.4 12.3364H7.69999C8.47319 12.3364 9.09999 11.6649 9.09999 10.8364V3.33643C9.09999 2.508 8.47319 1.83643 7.69999 1.83643Z" fill="white"/>
                  <path d="M13.65 2.81143C13.5439 2.74491 13.4234 2.70948 13.3006 2.70869C13.1777 2.7079 13.0568 2.74178 12.95 2.80693L10.5 4.29943V9.94917L12.9255 11.5992C13.0316 11.6712 13.1539 11.7113 13.2794 11.7152C13.405 11.7192 13.5292 11.6869 13.6391 11.6217C13.749 11.5565 13.8405 11.4608 13.904 11.3447C13.9675 11.2285 14.0006 11.0963 14 10.9617V3.46168C14.0001 3.3299 13.9678 3.20041 13.9064 3.08626C13.8449 2.97211 13.7565 2.87731 13.65 2.81143Z" fill="white"/>
                  </g>
                  </g>
                  </svg>
                  <p className="text-sm font-medium mb-0">Join Meeting</p>
                </button>
              </div>
              <p class="text-gray-500 text-sm font-medium">No one is in the call yet</p>
              <p onClick={this.handleLeaveSession} class="text-[#1C64F2] text-sm font-medium cursor-pointer">Dismiss</p>
            </div>

          </div>
</div>
    );
  }
}


const mapStateToProps = state => {
  return {
    SocketId: state.Call.SocketId,
    Peers: state.Call.peers,
    LocalStream:state.Call.LocalStream,
    Video:state.Call.Video,
    Audio: state.Call.Audio,
    Auth: state.Auth.auth,
    DummyAudio:state.Call.DummyAudio,
    UserName: state.Call.UserName,
    Config: state.Call.config,
    CameraAccess: state.Call.HasCamera,
    MicrophoneAccess: state.Call.HasMicrophone,
    // Additional props needed for proper toggle functionality
    ScreenShare: state.Call.ScreenShare,
    roomId: state.Call.roomId || window.location.pathname.split('/').pop() // Fallback to URL
  }
}

const mapDispatchToProps = {
    ...ExceptionActions,
    ...AuthException,
    ...Sessions,
    ...HostActions,
}

export default connect(mapStateToProps, mapDispatchToProps)(MeetingScreen)

