import React from 'react';
import { connect } from 'react-redux';
import * as HostActions from "../../../Actions/HostAction"
import  Firebase  from '../../../config/Firebase';
import * as ExceptionActions from "../../../Actions/Exception"
import * as AuthException from "../../../Actions/Auth"
import { PostRequestWithHeaders} from "../../../Tools/helpers/api"
class Index extends React.Component {
  constructor(props) {
    super(props);

      this.state = {
        sessionData:null,
        // redirectTo:false,
      }
      this.StartSession  = this.StartSession.bind(this)
      // console.log("props", this.props.id)
      Firebase.firestore().collection("sessions").doc(this.props.id).get().then((doc)=>{
        // console.log(doc.data())
        this.setState({
          sessionData:doc.data(),
            })

        if (doc.data().status === "ended" || doc.data().status === "canceled") {
          window.location = '/projects'
          return
        }else {
          return this.StartSession()
        }
        // if(doc.data().status=="ended" ||  doc.data().status=="canceled"){
        //   window.location = '/projects'
        //   return
        // }else if(doc.data().status=="on-going"){
        //     const ScheduleDate = new Date(doc.data().start);
        //     const currtime = new Date();
        //     const endTime = new Date(doc.data().end_time)
        //     if (ScheduleDate <= currtime && currtime < endTime) {
        //       return window.location = `/salestool/room/${this.props.id}`;
        //     }else{
        //       this.setState({redirectTo:true});
        //       setTimeout(() => {
        //         return window.location = '/projects'
        //       }, 2000); 
        //     }
        //   }else if(doc.data().status === "scheduled" && doc.data().type === "pixel_streaming"){
        //     return window.location = `/salestool/room/${this.props.id}`;
        //   }else{
        //   const ScheduleDate = new Date(doc.data().start);
        //   const currtime = new Date();
        //   const endTime = new Date(doc.data().end_time)
        //   if (ScheduleDate <= currtime && currtime < endTime) {
        //     this.StartSession()
        //     this.setState({
        //     session:doc.data(),
        //   })
        //   }
        //   this.setState({redirectTo:true});
        //     setTimeout(() => {
        //       return window.location = '/projects'
        //     }, 2000); 
        // }

      })
  }
  StartSession(){
    PostRequestWithHeaders({url:process.env.REACT_APP_API_BACKEND_API_URL+"session/StartSession",body:{
      "session_id":this.props.id,
    }}).then(response=>{
      window.location = `/salestool/room/${response._id}`;
    })
  }


render() {
   return (<>

   {this.state.sessionData?<>
    <div style={{ width: "100%", height: "100%" }}>
            <div
              style={{
                margin: "auto",
                width: 50,
                position: "relative",
                height: 50,
                top: "calc(50% - 80px)"
              }}
            >
              <div className="lds-ring">
                <div />
                <div />
                <div />
                <div />
              </div>
            </div>
          </div></>:<>
         <div style={{ width: "100%", height: "100%" }}>
            <div
              style={{
                margin: "auto",
                width: 50,
                position: "relative",
                height: 50,
                top: "calc(50% - 80px)"
              }}
            >
              <div className="lds-ring">
                <div />
                <div />
                <div />
                <div />
              </div>
            </div>
          </div>
        </>}


  </>)
    }
}
const mapStateToProps = state => {
  return {
    SETUP: state.Call.SETUP,
    ModalException: state.Exception.modalexception,
    DummyAudio: state.Call.DummyAudio,
    LocalStream: state.Call.LocalStream,
    Auth: state.Auth.auth,
    Plan: state.Auth.plan,
    Config: state.Call.config,
  }
}

const mapDispatchToProps = {
  ...HostActions,
  ...ExceptionActions,
  ...AuthException
}

export default connect(mapStateToProps, mapDispatchToProps)(Index)
