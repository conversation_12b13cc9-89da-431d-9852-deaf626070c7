import React from 'react';
import { connect } from 'react-redux';
import '../../../styles/video.css';
import * as HostActions from "../../../Actions/HostAction";
import { socket, peersRef } from "../../../Actions/HostAction"
import SideBar from './SideBar';
import * as ExceptionActions from "../../../Actions/Exception";
import RealTimeControls from "./RealTimeControls"
import TypeSwitch from './TypeSwitch';
import NewRealTImeController from './NewRealTImeController';
import { addScreenResizeListener, getScreenSize } from '../../../utils/screenUtils';
import ChatSidebar from './ChatSidebar';


class Room extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      platform: window.vuplex ? false : true,
      fullscreen: false,
      time: new Date(),
      showParticipants: true,
      screenSize: getScreenSize(),
      isScreenSharingPinned: false // Pin state for screen sharing
    }
    this.Sidenav = React.createRef();
    this.PinVideo = React.createRef()
    this.findPeer = this.findPeer.bind(this);
    this.SendDatabyChannel = this.SendDatabyChannel.bind(this);
    this.DataChannelMessage = this.DataChannelMessage.bind(this);
    this.togglePin = this.togglePin.bind(this);
    this.props.ConnecToWebSocket(this.props.roomId, this.props.Auth?.first_name || this.props.Auth?.email || "Host")
    this.props.SetVideoDevices();
    const Avatarsrc = ["https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar1.png",
      "https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar2.png",
      "https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar3.png",
      "https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar4.png",
      "https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar5.png",
      "https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar6.png",
    ]
    const extra = { color: Math.floor(Math.random() * 16777215).toString(16), username: (this.props.Auth.UserName ? this.props.Auth.UserName : this.props.Auth.DisplayName) + "(Host)", type: "host", id: socket.id, Avatar: Avatarsrc[Math.floor(Math.random() * 5)] }
    //
    socket.on('FE-user-leave', ({ userId, userName }) => {
      const peerIdx = this.findPeer(userId);
      if (peerIdx) {
        peerIdx.peer.destroy();

        this.props.UpdatePeer(this.props.Peers, userId)
      }

    });
    // Store the host's actual name to ensure consistent color generation
    const hostRealName = this.props.Auth.first_name || this.props.Auth.email || "Host";
    // Store this in localStorage so it can be accessed by other components
    localStorage.setItem('hostRealName', hostRealName);

    // Check if coming from MeetingScreen and sync localStorage preferences
    const isVideoOn = localStorage.getItem('isVideoOn') === 'true';
    const isMicOn = localStorage.getItem('isMicOn') === 'true';

    // Use MeetingScreen preferences if they exist, otherwise use current props
    const actualVideoState = localStorage.getItem('isVideoOn') !== null ? isVideoOn : this.props.Video;
    const actualAudioState = localStorage.getItem('isMicOn') !== null ? isMicOn : this.props.Audio;

    // Update Redux state to match actual preferences
    if (actualVideoState !== this.props.Video) {
      this.props.ToogleLocalAV("Video", actualVideoState);
    }
    if (actualAudioState !== this.props.Audio) {
      this.props.ToogleLocalAV("Audio", actualAudioState);
    }

    // Ensure userVideoAudio state is properly set before joining the room
    this.props.SetUserAV("localUser", {
      video: actualVideoState,
      audio: actualAudioState,
      screen: false
    });

    // Now join the room with the actual audio/video state
    socket.emit('BE-join-room', {
      roomId: this.props.roomId,
      userName: hostRealName + " (Host)",
      extra: JSON.stringify(extra),
      audio: actualAudioState,
      video: actualVideoState,
      screen: false
    });

    // Force broadcast actual video/audio state after joining
    // This ensures other participants see the correct initial state
    setTimeout(() => {
      console.log('Host broadcasting actual state:', { video: actualVideoState, audio: actualAudioState });

      // Broadcast current video state to all participants
      socket.emit('BE-toggle-camera-audio', {
        roomId: this.props.roomId,
        switchTarget: "Video",
        value: actualVideoState
      });

      // Broadcast current audio state to all participants
      socket.emit('BE-toggle-camera-audio', {
        roomId: this.props.roomId,
        switchTarget: "Audio",
        value: actualAudioState
      });
    }, 1000); // Small delay to ensure room join is complete

    // Listen for new users joining and broadcast current state to them
    this.newUserJoinHandler = (users) => {
      console.log('New user(s) joined, broadcasting current state');

      // Small delay to ensure the new user is ready to receive broadcasts
      setTimeout(() => {
        // Broadcast current video state
        socket.emit('BE-toggle-camera-audio', {
          roomId: this.props.roomId,
          switchTarget: "Video",
          value: this.props.Video
        });

        // Broadcast current audio state
        socket.emit('BE-toggle-camera-audio', {
          roomId: this.props.roomId,
          switchTarget: "Audio",
          value: this.props.Audio
        });
      }, 500);
    };

    socket.on('FE-user-join', this.newUserJoinHandler);
    //
  }

  findPeer(id) {
    return peersRef.find((p) => p.peerID === id);
  }

  // Toggle pin state for screen sharing
  togglePin() {
    this.setState(prevState => {
      const newPinnedState = !prevState.isScreenSharingPinned;

      // If screen sharing is being unpinned (moving to participants tab)
      // and participants tab is not open, automatically open it
      if (prevState.isScreenSharingPinned && !newPinnedState && !this.props.MembersOpen) {
        // Open participants tab so user can see the screen sharing content
        this.props.ToggleMembers(true);
      }

      return {
        isScreenSharingPinned: newPinnedState
      };
    });
  }
  componentDidMount() {
    this.removeResizeListener = addScreenResizeListener((newSize) => {
      this.setState({ screenSize: newSize });
    });
    setInterval(() => {
      this.setState({
        time: new Date()
      })
    }, 1000);
    // Check if LocalVideo element exists before setting srcObject
    const localVideoElement = document.getElementById("LocalVideo");
    if (localVideoElement) {
      localVideoElement.srcObject = this.props.LocalStream;
    }
    this.props.Call(this.props.LocalStream, this.DataChannelMessage)

    // Subscribe to chat messages at the Main component level
    // This ensures messages are received even when the chat sidebar is closed
    HostActions.SubscribeToChat((msg) => {
      const data = JSON.parse(msg);
      // Update unread count if chat is not open
      if (!this.props.ChatOpen) {
        this.props.SetUnreadMessage(this.props.UnreadCount + 1);
      }
      this.props.SetMessage(data);
    });
  }

  componentWillUnmount() {
    // Clean up screen size listener
    if (this.removeResizeListener) {
      this.removeResizeListener();
    }

    // Clean up socket listener
    if (this.newUserJoinHandler) {
      socket.off('FE-user-join', this.newUserJoinHandler);
    }

    // Unsubscribe from chat events
    HostActions.UnsubscribeFromChat();
  }


  DataChannelMessage(Peer, data) {
    let string = new TextDecoder().decode(data);
    let Data = JSON.parse(string)
    if (Data.type === "TRANSCRIPTION") {
      this.props.AddLocalTranscription(Data)

    }




  }

  SendDatabyChannel(Data) {
    Object.values(this.props.Peers).map(Peer => {
      if (Peer && Peer._channel) {
        if (Peer._channel.readyState == "open")
          Peer.send(JSON.stringify(Data))
      }

    })
  }
  toHumanReadableTime(isoString) {
    const date = new Date(isoString);

    const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;

    return formattedDate;
  }

    // Handle opening the invite modal
    handleOpenInviteModal = () => {
      // Open the share modal
      if (this.Sidenav && this.Sidenav.current) {
        this.Sidenav.current.open_close('share', true);
      }
    }

      // Handle closing the participants component
  handleCloseParticipants = () => {
    // Hide the participants component
    this.setState({ showParticipants: false });
  }


  render() {


    return (

      <>
        <div className="relative flex flex-col h-full w-full overflow-hidden">
          <div className="w-full">
            <NewRealTImeController roomId={this.props.roomId} isFullscreen={this.state.fullscreen} ShowSwitchProject={true} />

          </div>
          <div className="flex flex-row w-full h-screen flex-grow overflow-hidden">
            <div className={`relative ${this.state.screenSize === 'desktop' && (this.props.ChatOpen) ? 'w-[calc(100%-350px)]' : 'w-full'} h-full transition-all duration-300`}>
              <TypeSwitch
                platform={this.state.platform}
                SendDatabyChannel={this.SendDatabyChannel}
                SendCustomMessage={this.props.SendCustomMessage}
                roomId={this.props.roomId}
                isScreenSharingPinned={this.state.isScreenSharingPinned}
                onTogglePin={this.togglePin}
              />
            </div>

            {this.props.ChatOpen && (
              <div className="h-full  relative z-[99999]">
                <ChatSidebar
                  socketId={socket.id}
                  roomId={this.props.roomId}
                  isMobile={false}
                />
              </div>
            )}
            {/* Old SideBar removed to prevent duplicate chat message subscriptions */}
          </div>
          {/* <>
            <TypeSwitch platform={this.state.platform} SendDatabyChannel={this.SendDatabyChannel} SendCustomMessage={this.props.SendCustomMessage} roomId={this.props.roomId} />
          </> */}

        </div>


        {/* Always render SideBar component */}
        <SideBar
          sendmessage={this.sendmessage}
          PinVideo={this.PinVideo}
          socketid={socket.id}
          roomId={this.props.roomId}
          Sidenav={this.Sidenav}
          onTogglePin={this.togglePin}
          isScreenSharingPinned={this.state.isScreenSharingPinned}
        />


        {/* <RealTimeControls roomId={this.props.roomId} isFullscreen={this.state.fullscreen} ShowSwitchProject={true}/> */}

      </>
    );
  }
};



const mapStateTothisprops = state => {
  return {
    Peers: state.Call.peers,
    LocalStream: state.Call.LocalStream,
    UserName: state.Call.UserName,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    Auth: state.Auth.auth,
    Config: state.Call.config,
    ProjectDetails: state.Sessions.projectDetails,
    SessionDetails: state.Sessions.sessions,
    Tab: state.Call.Tab,
    ChatOpen: state.Call.ChatOpen,
    MembersOpen: state.Call.MembersOpen,
    ShowControls: state.Call.ShowControls,
    UnreadCount: state.Call.UnreadCount,
  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions,

}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(Room)