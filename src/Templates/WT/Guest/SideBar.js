import React from "react"
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction"
import VideoCard from "./VideoCard";
import * as ExceptionActions from "../../../Actions/Exception"
import Draggable from 'react-draggable';
import { InviteIcon, MultipleTabs } from '../../../assets/SvgIcons';
import './SideBar.css'; // Import the CSS file
import '../../../styles/avatar.css'; // Import avatar styles
import { getScreenSize, addScreenResizeListener } from '../../../utils/screenUtils';


class SideBar extends React.Component {
  constructor(props) {
    super(props);

    this.createUserVideo = this.createUserVideo.bind(this);
    this.localVideo = React.createRef();

    const screenSize = getScreenSize();

    this.state = {
      currentPosition: null,
      isDragging: false,
      position: { x: 20, y: 100 },
      isMaximized: true,
      isCollapsed: false,
      isMobile: screenSize === 'mobile',
      isTablet: screenSize === 'tablet',
      isContentVisible: true,
      isHovered: false,
      // Track peer statuses to persist across re-renders
      peerStatuses: {},
      screenSize: getScreenSize(),
      isSpeaking: false,
      audioLevel: 0,
      speakingThreshold: 10, // Threshold to detect speaking
      speakingTimeout: null
    };

    // Keep a ref to track attached streams to prevent unnecessary reattachment
    this.attachedStreams = new Set();
  }

  // Get user initials for avatar
  getInitials = (name) => {
    if (!name) return '?';

    // Remove the (Host) part if present
    const cleanName = name.replace(/\s*\(Host\)\s*$/i, '');

    const names = cleanName.split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    } else {
      return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
    }
  }

  // Generate consistent color based on name
  getColorFromName = (name) => {
    if (!name) return '3b82f6'; // Default blue

    // For host, use the stored real name to ensure consistency
    let nameToUse = name;
    if (name.includes('(Host)')) {
      // Use the stored host name if available
      const storedHostName = localStorage.getItem('hostRealName');
      if (storedHostName) {
        nameToUse = storedHostName;
      } else {
        // Remove the (Host) part if stored name not available
        nameToUse = name.replace(/\s*\(Host\)\s*$/i, '');
      }
    }

    // Simple hash function to generate a consistent color for the same name
    let hash = 0;
    for (let i = 0; i < nameToUse.length; i++) {
      hash = nameToUse.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Convert to hex color
    let color = Math.abs(hash).toString(16);
    // Pad with zeros if needed
    while (color.length < 6) {
      color = '0' + color;
    }
    // Take the last 6 characters to ensure valid hex color
    return color.substring(0, 6);
  }

  shouldComponentUpdate(nextProps) {
    // Check if sidebar should be open based on Tab state or MembersOpen/ChatOpen props
    const currentSidebarOpen = !!this.props.Tab;
    const nextSidebarOpen = !!nextProps.Tab || !!nextProps.MembersOpen || !!nextProps.ChatOpen;

    if (currentSidebarOpen !== nextSidebarOpen) {
      if (nextSidebarOpen) {
        // Don't use setState in shouldComponentUpdate as it causes infinite loops
        // Instead, update the DOM directly if needed
        const draggable = document.getElementById("LocalVideo");
        if (draggable && draggable.parentElement) {
          // Store the current position in a variable instead of state
          this._currentPosition = draggable.parentElement.style.transform;
          draggable.parentElement.style.transform = "translate(0px, 0px)";
        }
      } else {
        if (this._currentPosition) {
          const draggable = document.getElementById("LocalVideo");
          if (draggable && draggable.parentElement) {
            draggable.parentElement.style.transform = this._currentPosition;
          }
        }
      }
    }

    // Track peer status changes
    if (nextProps.Peers !== this.props.Peers) {
      // Update our internal peer status tracking
      const updatedStatuses = { ...this.state.peerStatuses };

      Object.values(nextProps.Peers || {}).forEach(peer => {
        if (peer && peer.peerID) {
          updatedStatuses[peer.peerID] = {
            username: peer.extra?.username || "Guest",
            audio: peer.audio,
            video: peer.video
          };
        }
      });

      // Only update state if there are actual changes to prevent re-renders
      if (JSON.stringify(updatedStatuses) !== JSON.stringify(this.state.peerStatuses)) {
        this.setState({ peerStatuses: updatedStatuses });
      }
    }

    return true;
  }

  componentDidMount() {
    // Add screen resize listener using the utility function
    this.removeResizeListener = addScreenResizeListener((newSize) => {
      // Update all related state at once
      this.setState({
        screenSize: newSize,
        isMobile: newSize === 'mobile',
        isTablet: newSize === 'tablet'
      });
    });

    // Add touch event listeners for mobile bottom sheet
    this.setupMobileBottomSheet();

    // Ensure the local user's video/audio state is initialized in userVideoAudio
    if (!this.props.userVideoAudio || !this.props.userVideoAudio["localUser"]) {
      this.props.SetUserAV("localUser", {
        video: this.props.Video,
        audio: this.props.Audio,
        screen: false
      });
    }

    // Use a small delay to ensure DOM is fully rendered
    setTimeout(() => {
      // Attach local video stream to all video elements
      const localVideo = document.getElementById("LocalVideo");
      const localVideoMobile = document.getElementById("LocalVideoMobile");
      const typeSwitchLocalVideo = document.getElementById("TypeSwitchLocalVideo");

      if (localVideo && this.props.LocalStream) {
        localVideo.srcObject = this.props.LocalStream;
      }

      if (localVideoMobile && this.props.LocalStream) {
        localVideoMobile.srcObject = this.props.LocalStream;
      }

      if (typeSwitchLocalVideo && this.props.LocalStream) {
        typeSwitchLocalVideo.srcObject = this.props.LocalStream;
      }

      // Set up audio analysis for speaking detection on local stream
      if (this.props.LocalStream && this.props.LocalStream.getAudioTracks().length > 0) {
        try {
          const audioContext = new (window.AudioContext || window.webkitAudioContext)();
          const analyser = audioContext.createAnalyser();
          analyser.fftSize = 32;
          const dataArray = new Uint8Array(analyser.frequencyBinCount);
          const audioSource = audioContext.createMediaStreamSource(this.props.LocalStream);
          audioSource.connect(analyser);

          // Check audio levels periodically
          this.audioLevelInterval = setInterval(() => {
            if (analyser && this.props.Audio) {
              analyser.getByteFrequencyData(dataArray);

              // Calculate average audio level
              const average = dataArray.reduce((sum, value) => sum + value, 0) / dataArray.length;

              // Update state based on audio level
              if (average > this.state.speakingThreshold) {
                if (!this.state.isSpeaking) {
                  this.setState({ isSpeaking: true, audioLevel: average });
                }

                // Clear any existing timeout
                if (this.state.speakingTimeout) {
                  clearTimeout(this.state.speakingTimeout);
                }

                // Set timeout to stop speaking state after silence
                const timeout = setTimeout(() => {
                  this.setState({ isSpeaking: false });
                }, 500);

                this.setState({ speakingTimeout: timeout });
              }
            }
          }, 100);
        } catch (error) {
          console.error('Error setting up audio analysis:', error);
        }
      }
    }, 300);
  }

  componentWillUnmount() {
    // Clean up screen resize listener
    if (this.removeResizeListener) {
      this.removeResizeListener();
    }

    // Clean up mobile bottom sheet event listeners
    if (this.state.isMobile || this.state.isTablet) {
      this.cleanupMobileBottomSheet();
    }

    // Clear our attached streams tracking
    this.attachedStreams.clear();

    // Clean up audio analysis interval
    if (this.audioLevelInterval) {
      clearInterval(this.audioLevelInterval);
    }

    // Clean up speaking timeout
    if (this.state.speakingTimeout) {
      clearTimeout(this.state.speakingTimeout);
    }
  }

  // Centralized function to attach all video streams
  attachAllVideoStreams = () => {
    // Handle local video stream
    const localVideo = document.getElementById("LocalVideo");
    if (localVideo && this.props.LocalStream && !this.attachedStreams.has('local')) {
      localVideo.srcObject = this.props.LocalStream;
      this.attachedStreams.add('local');
    }

    // Handle peer video streams
    if (this.props.Peers) {
      Object.values(this.props.Peers).forEach(peer => {
        if (peer && peer.peerID) {
          // We don't need to manually attach streams anymore
          // VideoCard will handle this through peer.on('stream')
          if (!this.attachedStreams.has(peer.peerID)) {
            this.attachedStreams.add(peer.peerID);

            // Still track peer status for our internal state
            this.setState(prevState => ({
              peerStatuses: {
                ...prevState.peerStatuses,
                [peer.peerID]: {
                  username: peer.extra?.username || "Guest",
                  audio: peer.audio,
                  video: peer.video
                }
              }
            }));
          }
        }
      });
    }
  };

  // Handle screen size change
  handleScreenSizeChange = () => {
    const screenSize = getScreenSize();
    const isMobile = screenSize === 'mobile';
    const isTablet = screenSize === 'tablet';

    // Only update state if the screen size has changed
    if (this.state.isMobile !== isMobile || this.state.isTablet !== isTablet) {
      // Just update the screen size state
      this.setState({ isMobile, isTablet }, () => {
        // Setup or cleanup mobile sheet based on new screen size
        if (isMobile || isTablet) {
          this.setupMobileBottomSheet();
        } else {
          this.cleanupMobileBottomSheet();
        }

        // Clear our attachment tracking to ensure videos are reattached
        this.attachedStreams.clear();

        // Reattach all streams after a small delay to ensure DOM is updated
        setTimeout(() => {
          this.attachAllVideoStreams();
        }, 200);
      });
    }
  };

  // Set up mobile bottom sheet drag behavior
  setupMobileBottomSheet = () => {
    setTimeout(() => {
      const sheet = document.getElementById('mobile-participants-sheet');
      const handle = document.getElementById('mobile-participants-handle');

      if (!sheet || !handle) return;

      let startY = 0;
      let currentY = 0;

      const handleTouchStart = (e) => {
        startY = e.touches[0].clientY;
        sheet.style.transition = 'none';
      };

      const handleTouchMove = (e) => {
        currentY = e.touches[0].clientY - startY;
        if (currentY > 0) {
          sheet.style.transform = `translateY(${currentY}px)`;
        }
      };

      const handleTouchEnd = () => {
        sheet.style.transition = 'transform 0.3s ease-out';
        if (currentY > 100) {
          // If dragged down more than 100px, close the sheet and turn off member tab
          this.props.ToggleMembers(false);
          this.props.TabControl(false); // Turn off the member tab
        } else {
          // Reset position
          sheet.style.transform = 'translateY(0)';
        }
      };

      handle.addEventListener('touchstart', handleTouchStart);
      handle.addEventListener('touchmove', handleTouchMove);
      handle.addEventListener('touchend', handleTouchEnd);

      // Store event listeners for cleanup
      this.mobileSheetListeners = {
        handle,
        events: [
          { type: 'touchstart', fn: handleTouchStart },
          { type: 'touchmove', fn: handleTouchMove },
          { type: 'touchend', fn: handleTouchEnd }
        ]
      };
    }, 100);
  };

  // Clean up mobile bottom sheet event listeners
  cleanupMobileBottomSheet = () => {
    if (this.mobileSheetListeners) {
      const { handle, events } = this.mobileSheetListeners;
      events.forEach(({ type, fn }) => {
        handle.removeEventListener(type, fn);
      });
    }
  };

  // Updates the video element when props change or state changes
  componentDidUpdate(prevProps, prevState) {
    // Handle screen size changes
    if (prevState.isMobile !== this.state.isMobile || prevState.isTablet !== this.state.isTablet) {
      // If we switched to mobile/tablet view, we need to handle it differently
      if (this.state.isMobile || this.state.isTablet) {
        // We'll handle this in setupMobileBottomSheet
        return;
      }
    }

    // Only update local video if stream changes
    if (prevProps.LocalStream !== this.props.LocalStream) {
      // Update all local video elements
      const localVideo = document.getElementById("LocalVideo");
      const localVideoMobile = document.getElementById("LocalVideoMobile");
      const typeSwitchLocalVideo = document.getElementById("TypeSwitchLocalVideo");

      if (localVideo && this.props.LocalStream) {
        localVideo.srcObject = this.props.LocalStream;
      }

      if (localVideoMobile && this.props.LocalStream) {
        localVideoMobile.srcObject = this.props.LocalStream;
      }

      if (typeSwitchLocalVideo && this.props.LocalStream) {
        typeSwitchLocalVideo.srcObject = this.props.LocalStream;
      }
    }

    // Check for changes in peers
    if (prevProps.Peers !== this.props.Peers) {
      // Get current and new peer IDs
      const currentPeerIds = Object.keys(prevProps.Peers || {});
      const newPeerIds = Object.keys(this.props.Peers || {});

      // Find peers that are new
      const addedPeers = newPeerIds.filter(id => !currentPeerIds.includes(id));

      // Track new peers in our state
      addedPeers.forEach(peerId => {
        const peer = this.props.Peers[peerId];
        if (peer && peer.peerID) {
          // Mark as attached
          this.attachedStreams.add(peer.peerID);

          // Update our peer status tracking
          this.setState(prevState => ({
            peerStatuses: {
              ...prevState.peerStatuses,
              [peer.peerID]: {
                username: peer.extra?.username || "Guest",
                audio: peer.audio,
                video: peer.video
              }
            }
          }));
        }
      });

      // Also check for status updates on existing peers
      newPeerIds.forEach(peerId => {
        const peer = this.props.Peers[peerId];
        const prevPeer = prevProps.Peers[peerId];

        if (peer && prevPeer && (peer.audio !== prevPeer.audio || peer.video !== prevPeer.video)) {
          // Update our tracking if audio or video status changed
          this.setState(prevState => ({
            peerStatuses: {
              ...prevState.peerStatuses,
              [peerId]: {
                ...prevState.peerStatuses[peerId],
                username: peer.extra?.username || "Guest",
                audio: peer.audio,
                video: peer.video
              }
            }
          }));
        }
      });
    }

    // If MembersOpen changed, handle it
    if (prevProps.MembersOpen !== this.props.MembersOpen && this.props.MembersOpen) {
      // Reset the bottom sheet position when opening
      const sheet = document.getElementById('mobile-participants-sheet');
      if (sheet) {
        sheet.style.transform = 'translateY(0)';
      }

      // Force a re-render to ensure videos are reattached
      this.forceUpdate();
    }

    // If userVideoAudio changed, update our peer status tracking
    if (prevProps.userVideoAudio !== this.props.userVideoAudio) {
      // Update our peer status tracking based on userVideoAudio changes
      const updatedStatuses = { ...this.state.peerStatuses };

      Object.entries(this.props.userVideoAudio).forEach(([peerId, status]) => {
        if (updatedStatuses[peerId]) {
          updatedStatuses[peerId] = {
            ...updatedStatuses[peerId],
            audio: status.audio,
            video: status.video
          };
        }
      });

      // Only update state if there are actual changes
      if (JSON.stringify(updatedStatuses) !== JSON.stringify(this.state.peerStatuses)) {
        this.setState({ peerStatuses: updatedStatuses });
      }
    }
  }

  // Track position when dragging starts
  handleDragStart = () => {
    this.setState({ isDragging: true });
  }

  // Update position when dragging ends
  handleDragStop = (_, data) => {
    this.setState({
      isDragging: false,
      position: { x: data.x, y: data.y }
    });
  }

  // Mouse event handlers
  handleMouseEnter = () => {
    this.setState({ isHovered: true });
  };

  handleMouseLeave = () => {
    this.setState({ isHovered: false });
  };

  // Toggle maximize
  handleMaximizeMembersVideo = () => {
    this.setState(prevState => ({
      isMaximized: !prevState.isMaximized
    }), () => {
      // Clear our attachment tracking and reattach all videos
      this.attachedStreams.clear();
      setTimeout(() => {
        this.attachAllVideoStreams();
      }, 50);
    });
  };

  // Handle collapsing/expanding the participants component
  handleToggleCollapse = () => {
    this.setState(prevState => ({
      isCollapsed: !prevState.isCollapsed
    }), () => {
      if (!this.state.isCollapsed) {
        // Only reattach if we're expanding the component
        this.attachedStreams.clear();
        setTimeout(() => {
          this.attachAllVideoStreams();
        }, 50);
      }
    });
  }

  // Touch event handlers
  handleTouchStart = () => {
    this.setState({ isHovered: true });
  };

  handleTouchEnd = () => {
    this.setState({ isHovered: false });
  };

  // Create video card for each peer
  createUserVideo(peer) {
    return (
      <div key={peer.peerID} className="video-item">
        <VideoCard
          tab={this.props.Tab}
          peer={peer}
          userVideoAudio={this.props.userVideoAudio}
          onTogglePin={this.props.onTogglePin}
        />
      </div>
    );
  }

  // Handle opening the invite modal
  handleOpenInviteModal = () => {
    // For guest, we can show a message that they can't invite others
    this.props.SetModalException("Only the host can invite participants");
  }

  // Render screen sharing VideoCard when unpinned
  renderScreenSharingCard() {
    // Check if screen sharing is active
    const sharingPeer = Object.values(this.props.Peers || {}).find(peer =>
      peer && peer.peerID && this.props.userVideoAudio[peer.peerID] && this.props.userVideoAudio[peer.peerID].screen
    );

    if (!sharingPeer) return null;

    // Check if both screen sharing and project content are active
    const hasProject = this.props.Config && this.props.Config.project_id && this.props.Config.type !== 'default';
    const isScreenSharingPinned = this.props.isScreenSharingPinned || false;

    // Only show screen sharing card when both are active and screen sharing is unpinned
    if (!hasProject || isScreenSharingPinned) return null;

    // Check if we're in mobile view
    const isMobile = this.state.screenSize === 'mobile';

    if (isMobile) {
      // Mobile version - match the mobile VideoCard style
      return (
        <div key="screen-sharing-card-mobile" className="relative rounded-lg overflow-hidden bg-black shadow-lg min-w-[200px] w-[200px] flex-shrink-0 aspect-video">
          <div className="w-full h-full relative bg-black">
            {/* Screen sharing video */}
            <video
              className="w-full h-full object-cover bg-black"
              id={sharingPeer.peerID + "_SCREEN_PARTICIPANTS_MOBILE"}
              playsInline
              autoPlay
              ref={(ref) => {
                if (ref) {
                  // Get the screen sharing stream from the source video element
                  const sourceVideo = document.getElementById(sharingPeer.peerID + "VIDEO");
                  if (sourceVideo && sourceVideo.srcObject) {
                    ref.srcObject = sourceVideo.srcObject;
                  } else {
                    // Retry after a short delay if source video not ready
                    setTimeout(() => {
                      const retrySourceVideo = document.getElementById(sharingPeer.peerID + "VIDEO");
                      if (retrySourceVideo && retrySourceVideo.srcObject) {
                        ref.srcObject = retrySourceVideo.srcObject;
                      }
                    }, 100);
                  }
                }
              }}
            />

            {/* Pin icon */}
            <button
              onClick={() => this.props.onTogglePin && this.props.onTogglePin()}
              className="absolute top-2 right-2 z-10 bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-lg transition-colors"
              title="Pin screen sharing to main area"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
              </svg>
            </button>

            {/* Screen sharing indicator text */}
            <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
              {sharingPeer.extra && sharingPeer.extra.type === 'host' ? 'Host is presenting' : `${sharingPeer.userName} is presenting`}
            </div>
          </div>
        </div>
      );
    }

    // Desktop version - match the desktop VideoCard style
    return (
      <div key="screen-sharing-card" className="video-item">
        <div className="custom-aspect-ratio aspect-video fixed-video">
          <div className="VideoControls_Div">
            <div className="VideoOffname" style={{ marginRight: "8px", textAlign: "start" }}>
              <div style={{ position: "absolute", width: "100%", bottom: "0px", padding: "0 0 0 4px" }}>
                {sharingPeer.extra && sharingPeer.extra.type === 'host' ? 'Host is presenting' : `${sharingPeer.userName} is presenting`}
              </div>
            </div>
          </div>

          {/* Screen sharing video */}
          <video
            className="user-video w-full h-full object-cover bg-black"
            id={sharingPeer.peerID + "_SCREEN_PARTICIPANTS"}
            playsInline
            autoPlay
            ref={(ref) => {
              if (ref) {
                // Get the screen sharing stream from the source video element
                const sourceVideo = document.getElementById(sharingPeer.peerID + "VIDEO");
                if (sourceVideo && sourceVideo.srcObject) {
                  ref.srcObject = sourceVideo.srcObject;
                } else {
                  // Retry after a short delay if source video not ready
                  setTimeout(() => {
                    const retrySourceVideo = document.getElementById(sharingPeer.peerID + "VIDEO");
                    if (retrySourceVideo && retrySourceVideo.srcObject) {
                      ref.srcObject = retrySourceVideo.srcObject;
                    }
                  }, 100);
                }
              }
            }}
          />

          {/* Pin icon */}
          <button
            onClick={() => this.props.onTogglePin && this.props.onTogglePin()}
            className="absolute top-2 right-2 z-10 bg-black/50 hover:bg-black/70 text-white p-1.5 rounded-lg transition-colors"
            title="Pin screen sharing to main area"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
            </svg>
          </button>
        </div>
      </div>
    );
  }

  render() {
    const { isMaximized, isCollapsed } = this.state;

    // Check if sidebar should be open based on Tab state or MembersOpen props
    const isSidebarOpen = !!this.props.Tab || !!this.props.MembersOpen;

    // Always show MEMBERS tab, never show CHAT tab
    const activeTab = this.props.Tab || (this.props.MembersOpen ? "MEMBERS" : false);

    // Check if type is 'default' or project ID is undefined
    const isDefault = this.props.Config && this.props.Config.type === 'default';
    const noProjectId = !this.props.Config || !this.props.Config.project_id;
    // Check if anyone is sharing their screen
    const isAnyoneScreenSharing = this.props.Peers && Object.values(this.props.Peers).some(peer =>
      peer && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
      this.props.userVideoAudio[peer.peerID].screen
    );

    const shouldShowInTypeSwitch = isDefault || noProjectId;

    // Container style for the draggable component
    const containerStyle = {
      position: 'fixed',
      background: 'white',
      borderRadius: '12px',
      padding: '0',
      zIndex: 1000,
      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.3s ease-in-out, transform 0.1s linear',
      maxHeight: isCollapsed ? '40px' : '1000px',
      width: '300px',
      display: this.props.MembersOpen && !this.state.isMobile && !this.state.isTablet ? 'block' : 'none',
      willChange: 'transform',
      transform: this.state.isDragging ? 'scale(1.02)' : 'scale(1)',
      overflow: 'hidden',
      top: '100px', // Position it below the top controls
      left: '20px', // Position it from the left edge
    };

    return (
      <>
        {/* Keep the original sidebar div for reference */}
        <div id="mySidenav" className={`${activeTab === "MEMBERS" ? "" : ""} `}
          style={{ marginLeft: isSidebarOpen ? "0px" : "0px", position: isSidebarOpen ? 'fixed' : 'relative' }} ref={this.props.Sidenav}>
          {/* Empty div to maintain the sidebar structure */}
        </div>

        {/* Floating local video for TypeSwitch - Only visible when type is 'default' or project ID is undefined */}
        <div style={{ height: shouldShowInTypeSwitch ? "-webkit-fill-available" : undefined }}
          className={`fixed aspect-video overflow-hidden shadow-lg
            ${shouldShowInTypeSwitch ? 'block rounded-none  top-12' : 'hidden w-64 rounded-lg  top-20'}
            ${this.state.screenSize === 'desktop' && this.props.ChatOpen && shouldShowInTypeSwitch ? 'w-[calc(100%-350px)]' : 'w-full'}
          `}
        >
          <video
            id="TypeSwitchLocalVideo"
            className={`w-full h-full object-cover ${this.props.userVideoAudio && this.props.userVideoAudio.localUser && this.props.userVideoAudio.localUser.screen ? 'ScreenSharing' : ''}`}
            autoPlay
            muted
            playsInline
          />

          {/* Avatar with first letter when video is off in TypeSwitch */}
          {(this.props.Video === false) && (
            <div
              className="absolute inset-0 flex items-center justify-center"
              style={{
                background: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), #${this.getColorFromName(this.props.UserName || "Guest")}`
              }}
            >
              <div
                className="avatar-bg"
                style={{
                  background: `radial-gradient(circle, #${this.getColorFromName(this.props.UserName || "Guest")}30 0%, rgba(0,0,0,0) 70%)`
                }}
              ></div>
              <div
                className={`relative w-40 h-40 rounded-full flex items-center justify-center avatar-circle ${this.state.isSpeaking && this.props.Audio ? 'avatar-speaking' : ''}`}
                style={{
                  backgroundColor: `#${this.getColorFromName(this.props.UserName || "Guest")}`,
                  boxShadow: `0 0 30px #${this.getColorFromName(this.props.UserName || "Guest")}80`
                }}
              >
                <span className="text-white text-5xl font-bold">
                  {this.getInitials(this.props.UserName || "Guest")}
                </span>
              </div>
            </div>
          )}

          <div className="absolute bottom-0 left-0 right-0 text-center bg-black/50 text-white text-xs py-1 px-1 font-medium">
            {this.props.UserName || "Guest"}
          </div>
        </div>

        {/* Mobile participants UI - Only visible on mobile/tablet */}
        <div id="mobile-participants-sheet" className="fixed bottom-0 left-0 right-0 z-[9999] bg-white rounded-t-xl shadow-lg transition-transform duration-300 md:hidden"
          style={{ display: this.props.MembersOpen && (this.state.isMobile || this.state.isTablet) ? 'block' : 'none' }}>
          {/* Handle for dragging */}
          <div id="mobile-participants-handle" className="w-full flex justify-center py-2 cursor-grab active:cursor-grabbing">
            <div className="w-12 h-1 bg-gray-300 rounded-full"></div>
          </div>

          {/* Header */}
          <div className="px-4 flex justify-between items-center ">
            <p className="text-[16px] font-semibold">Participants</p>
            <button
              onClick={this.handleOpenInviteModal}
              className="p-2 rounded-lg hover:bg-gray-100 bg-[#F3F4F6]"
            >
              <InviteIcon />
            </button>
          </div>
          <div className="p-3 overflow-x-auto pb-safe">
            <div className="flex overflow-x-auto gap-2 pb-2 no-scrollbar" style={{ flexWrap: 'nowrap' }}>
              {/* Local user (You) - Show when screen sharing is active or when not in default mode */}
              <div
                className="relative rounded-lg overflow-hidden bg-black shadow-lg min-w-[200px] w-[200px] flex-shrink-0 aspect-video"
                style={{ display: (!shouldShowInTypeSwitch || isAnyoneScreenSharing) ? 'block' : 'none' }}
              >
                <video
                  id="LocalVideoMobile"
                  className={`w-full h-full object-cover ${this.props.userVideoAudio && this.props.userVideoAudio.localUser && this.props.userVideoAudio.localUser.screen ? 'ScreenSharing' : ''}`}
                  autoPlay
                  muted
                  playsInline
                />

                {/* Avatar with first letter when video is off */}
                {(this.props.Video === false) && (
                  <div
                    className="absolute inset-0 flex items-center justify-center"
                    style={{
                      background: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), #${this.getColorFromName(this.props.UserName || "Guest")}`
                    }}
                  >
                    <div
                      className="avatar-bg"
                      style={{
                        background: `radial-gradient(circle, #${this.getColorFromName(this.props.UserName || "Guest")}30 0%, rgba(0,0,0,0) 70%)`
                      }}
                    ></div>
                    <div
                      className={`relative w-16 h-16 rounded-full flex items-center justify-center avatar-circle ${this.state.isSpeaking && this.props.Audio ? 'avatar-speaking' : ''}`}
                      style={{
                        backgroundColor: `#${this.getColorFromName(this.props.UserName || "Guest")}`,
                        boxShadow: `0 0 30px #${this.getColorFromName(this.props.UserName || "Guest")}80`
                      }}
                    >
                      <span className="text-white text-xl font-bold">
                        {this.getInitials(this.props.UserName || "Guest")}
                      </span>
                    </div>
                  </div>
                )}

                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent h-8"></div>

                {/* User name and mic container */}
                <div className="absolute bottom-0 w-full px-2 py-1 flex items-center justify-between">
                  <div className="text-white text-xs font-medium truncate max-w-[80%]">
                    You
                  </div>
                </div>
              </div>

              {/* Screen sharing VideoCard - show at top when screen sharing is unpinned */}
              {this.renderScreenSharingCard()}

              {/* Remote participants */}
              {this.props.Peers && Object.values(this.props.Peers).map((peer) => {
                if (peer != null && peer.peerID) {

                  return (
                    <div key={peer.peerID + "_mobile"} className="relative rounded-lg overflow-hidden bg-black shadow-lg min-w-[200px] w-[200px] flex-shrink-0 aspect-video">
                      {/* We'll use VideoCard for consistency, but with a different ID */}
                      <VideoCard
                        tab={this.props.Tab}
                        peer={peer}
                        userVideoAudio={this.props.userVideoAudio}
                        onTogglePin={this.props.onTogglePin}
                      />
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </div>
        </div>

        {/* Draggable participants component - Only visible on desktop */}
        <Draggable
          bounds="parent"
          defaultPosition={this.state.position}
          position={this.state.isDragging ? null : this.state.position}
          onStart={this.handleDragStart}
          onStop={this.handleDragStop}
          handle=".drag-handle"
          positionOffset={{ x: '0', y: '0' }}
        >
          <div style={containerStyle} className="hidden lg:block">
            {/* Drag handle at the top */}
            <div className="drag-handle w-full py-1 px-2 cursor-move text-center text-black text-sm font-medium flex justify-between items-center bg-gray-50 border-b">
              <button
                onClick={this.handleOpenInviteModal}
                className="flex items-center text-black justify-between bg-[#E5E7EB] gap-2 p-1.5 rounded-md"
              >
                <InviteIcon />
                <span className="text-md">Invite</span>
              </button>
              <div className="flex space-x-1">
                {/* Only show maximize/minimize button if there are peers */}
                {this.props.Peers && Object.keys(this.props.Peers).length > 0 && !isCollapsed && (
                  <button
                    onClick={this.handleMaximizeMembersVideo}
                    className="p-1 rounded-full hover:bg-gray-200"
                    title={isMaximized ? "Show only one video" : "Show all videos"}
                  >
                    {isMaximized ? (
                      <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.5 3.5C3.67157 3.5 3 4.17157 3 5V12C3 12.8284 3.67157 13.5 4.5 13.5H11.5C12.3284 13.5 13 12.8284 13 12V5C13 4.17157 12.3284 3.5 11.5 3.5H4.5ZM4.5 4.5H11.5C11.7761 4.5 12 4.72386 12 5V12C12 12.2761 11.7761 12.5 11.5 12.5H4.5C4.22386 12.5 4 12.2761 4 12V5C4 4.72386 4.22386 4.5 4.5 4.5Z" fill="#6B7280" />
                      </svg>
                    ) : (
                      <MultipleTabs />
                    )}
                  </button>
                )}
                <button
                  onClick={this.handleToggleCollapse}
                  className="p-1 rounded-full hover:bg-gray-200"
                  title={isCollapsed ? "Expand" : "Collapse"}
                >
                  {isCollapsed ? (
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 8H13" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" />
                      <path d="M8 3L8 13" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" />
                    </svg>
                  ) : (
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 8H13" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" />
                    </svg>
                  )}
                </button>
              </div>
            </div>

            {/* Participants list */}
            <div className="transition-all duration-300 ease-in-out"
              style={{
                maxHeight: isCollapsed ? '0' : (isMaximized ? 'calc(80vh - 40px)' : '170px'),
                overflow: isMaximized ? 'auto' : 'hidden',
                opacity: isCollapsed ? 0 : 1,
                visibility: isCollapsed ? 'hidden' : 'visible'
              }}>
              <div className="p-1 flex flex-col gap-1">
                {/* Local video - Show when screen sharing is active or when not in default mode */}
                <div className="video-item" style={{ display: isCollapsed || (shouldShowInTypeSwitch && !isAnyoneScreenSharing) ? 'none' : 'block' }}>
                  <div
                    ref={this.localVideo}
                    className="relative rounded-lg overflow-hidden bg-black shadow-lg aspect-video"
                  >
                    <video
                      id="LocalVideo"
                      className={`w-full h-full object-cover ${this.props.userVideoAudio && this.props.userVideoAudio.localUser && this.props.userVideoAudio.localUser.screen ? 'ScreenSharing' : ''}`}
                      autoPlay
                      muted
                      playsInline
                    />

                    {/* Avatar with first letter when video is off */}
                    {(this.props.Video === false) && (
                      <div
                        className="absolute inset-0 flex items-center justify-center"
                        style={{
                          background: `linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), #${this.getColorFromName(this.props.UserName || "Guest")}`
                        }}
                      >
                        <div
                          className="avatar-bg"
                          style={{
                            background: `radial-gradient(circle, #${this.getColorFromName(this.props.UserName || "Guest")}30 0%, rgba(0,0,0,0) 70%)`
                          }}
                        ></div>
                        <div
                          className={`relative w-20 h-20 rounded-full flex items-center justify-center avatar-circle ${this.state.isSpeaking && this.props.Audio ? 'avatar-speaking' : ''}`}
                          style={{
                            backgroundColor: `#${this.getColorFromName(this.props.UserName || "Guest")}`,
                            boxShadow: `0 0 30px #${this.getColorFromName(this.props.UserName || "Guest")}80`
                          }}
                        >
                          <span className="text-white text-2xl font-bold">
                            {this.getInitials(this.props.UserName || "Guest")}
                          </span>
                        </div>
                      </div>
                    )}

                    <div className="absolute bottom-0 left-0 right-0 text-center bg-black/50 text-white text-xs py-1 px-1 font-medium">
                      You
                    </div>
                  </div>
                </div>



                {/* Screen sharing VideoCard - show at top when screen sharing is unpinned */}
                {this.renderScreenSharingCard()}

                {/* Show peers if they exist */}
                {this.props.Peers && Object.values(this.props.Peers).filter(peer => peer != null && peer.peerID).length > 0 ? (
                  Object.values(this.props.Peers).map((peer) => {
                    if (peer != null && peer.peerID) {
                      return this.createUserVideo(peer)
                    }
                    return ""
                  })
                ) : (
                  <div className="flex items-center justify-center p-4 text-gray-500 text-center">
                    <div>
                      <svg className="w-12 h-12 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                      </svg>
                      <p className="text-sm font-medium">No other participants</p>
                      <p className="text-xs mt-1">Invite others to join the meeting</p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </Draggable>
      </>
    )
  }
}

const mapStateTothisprops = state => {
  return {
    Peers: state.Call.peers,
    VideoDevices: state.Call.VideoDevices,
    SocketId: state.Call.SocketId,
    LocalStream: state.Call.LocalStream,
    UserName: state.Call.UserName,
    userVideoAudio: state.Call.userVideoAudio,
    Messages: state.Call.Messages,
    userscount: state.Call.userscount,
    Tab: state.Call.Tab,
    UnreadCount: state.Call.UnreadCount,
    ShowControls: state.Call.ShowControls,
    // Add these props to check for sidebar visibility from the new controller
    MembersOpen: state.Call.MembersOpen,
    ChatOpen: state.Call.ChatOpen,
    Config: state.Call.config,
    roomId: state.Call.roomId,
    Video: state.Call.Video,
    Audio: state.Call.Audio,

  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(SideBar)