import React from "react";
import 'jquery';

class CloseModal extends React.Component {
  constructor(props){
    super(props);
    this.state={
      redirect: false,
      isProcessing: false
    }
  }

  handleConfirm = async () => {
    this.setState({ isProcessing: true });
    
    try {
      if (this.props.CloseSession) {
        await this.props.CloseSession();
      }
    } catch (error) {
      console.error("Error in CloseSession:", error);
    } finally {
      this.setState({ isProcessing: false });
    }
  }

  handleCancel = () => {
    if (this.props.Close) {
      this.props.Close();
    }
  }
  
  render() {
    const {
      type = 'exit', // 'exit' or 'stopSharing'
    } = this.props;

    // Define messages based on type
    const messages = {
      exit: {
        message: 'Are you sure you want to Exit this Meeting?',
        confirmText: "Yes, I'm sure",
        cancelText: 'No, cancel'
      },
      stopSharing: {
        message: 'Are you sure you want to STOP SHARING The Project?',
        confirmText: 'Yes, stop sharing',
        cancelText: 'No, cancel'
      }
    };

    const currentMessage = messages[type] || messages.exit;

    if(this.state.redirect) {
      return window.location="/salestool/feedback" 
    }

    return( 
      <div className="bg-[#00000099] fixed z-[10501] overflow-hidden w-full h-full inset-0" id="close_modal" tabIndex="-1" role="dialog">
        <div className="mx-auto p-4 max-w-[448px] h-full w-full flex justify-center items-center" role="document">
          <div className="p-6 shadow-lg h-fit flex flex-col w-full bg-white rounded-lg">
            <div className="flex flex-col items-center text-center gap-2">
              {/* Warning Icon */}
              <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M10 0C8.02219 0 6.08879 0.58649 4.4443 1.6853C2.79981 2.78412 1.51809 4.3459 0.761209 6.17316C0.00433284 8.00042 -0.1937 10.0111 0.192152 11.9509C0.578004 13.8907 1.53041 15.6725 2.92894 17.0711C4.32746 18.4696 6.10929 19.422 8.0491 19.8078C9.98891 20.1937 11.9996 19.9957 13.8268 19.2388C15.6541 18.4819 17.2159 17.2002 18.3147 15.5557C19.4135 13.9112 20 11.9778 20 10C19.9971 7.34873 18.9426 4.80688 17.0679 2.93215C15.1931 1.05741 12.6513 0.0029116 10 0ZM10 15C9.80222 15 9.60888 14.9413 9.44443 14.8315C9.27998 14.7216 9.15181 14.5654 9.07612 14.3827C9.00044 14.2 8.98063 13.9989 9.01922 13.8049C9.0578 13.6109 9.15304 13.4327 9.2929 13.2929C9.43275 13.153 9.61093 13.0578 9.80491 13.0192C9.99889 12.9806 10.2 13.0004 10.3827 13.0761C10.5654 13.1518 10.7216 13.28 10.8315 13.4444C10.9414 13.6089 11 13.8022 11 14C11 14.2652 10.8946 14.5196 10.7071 14.7071C10.5196 14.8946 10.2652 15 10 15ZM11 11C11 11.2652 10.8946 11.5196 10.7071 11.7071C10.5196 11.8946 10.2652 12 10 12C9.73479 12 9.48043 11.8946 9.2929 11.7071C9.10536 11.5196 9 11.2652 9 11V6C9 5.73478 9.10536 5.48043 9.2929 5.29289C9.48043 5.10536 9.73479 5 10 5C10.2652 5 10.5196 5.10536 10.7071 5.29289C10.8946 5.48043 11 5.73478 11 6V11Z" fill="#9CA3AF"/>
              </svg>

              {/* Dynamic message */}
              <div className="">
                <p className="text-[16px] text-base text-[#6B7280] leading-[150%] tracking-[0%]">
                  {currentMessage.message}
                </p>
              </div>

              {/* Buttons */}
              <div className="flex gap-3 w-full">
                <button 
                  onClick={this.handleConfirm}
                  disabled={this.state.isProcessing}
                  type="button" 
                  className="flex-1 h-10 rounded bg-[#C81E1E] text-white text-sm hover:bg-[#b91c1c] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {this.state.isProcessing ? 'Processing...' : currentMessage.confirmText}
                </button>
                <button 
                  onClick={this.handleCancel}
                  disabled={this.state.isProcessing}
                  type="button" 
                  className="flex-1 h-10 rounded bg-transparent border border-[#6B7280] text-[#6B7280] font-['Inter'] text-sm hover:bg-gray-700/50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {currentMessage.cancelText}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default CloseModal;