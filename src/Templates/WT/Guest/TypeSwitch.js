import React from "react"
import Firebase from "../../../config/Firebase"
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction";
import { PostRequestWithHeaders } from "../../../Tools/helpers/api";
import { UpdateSessionApi } from "../../../Tools/helpers/BackendApis";
import PixelStreaming from "../../PixelStreaming/Guest/Pixel"
import Lark from "../../LarkXr/Guest/lark"
import AleGuest from "../../Ale/Guest/Scene"
import LoaderOverlay from "../../../components/LoaderOverlay";
import VideoCard from "./VideoCard";


class TypeSwitch extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            currentTime: new Date(),
            interval: 30,
            isSessionStarted: false,
            isSessionEnded: false,
            isNearingEnd: false,
            // Pin state is now managed by parent component
        }
        this.SetDuration = this.SetDuration.bind(this);
        this.togglePin = this.togglePin.bind(this);
        this.intervalId = null;
        this.job = null;
        this.attachedScreenVideos = new Set(); // Track attached screen sharing videos
        this.lastScreenSharingState = null; // Track screen sharing state changes

    }

    componentDidMount() {
        // console.log("componentDidMount",this.props.Config.end_time,this.props.Config)
        // Initialize component state and set up listeners --mayank
        if (!this.props.Config) {
            this.fetchConfigData();
        } else {
            this.initializeSessionState(this.props.Config);
        }

        // Fetch the nested config data
        this.fetchNestedConfigData();

        this.setupIntervals();
        this.setupSessionListener();
    }

    componentDidUpdate(prevProps) {
        // Handle stream updates for screen sharing - check both peers and userVideoAudio changes
        const prevSharingPeer = prevProps.Peers && Object.values(prevProps.Peers).find(peer =>
            peer && peer.peerID && prevProps.userVideoAudio && prevProps.userVideoAudio[peer.peerID] &&
            prevProps.userVideoAudio[peer.peerID].screen
        );

        const currentSharingPeer = this.props.Peers && Object.values(this.props.Peers).find(peer =>
            peer && peer.peerID && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
            this.props.userVideoAudio[peer.peerID].screen
        );

        // If screen sharing state changed (started or stopped)
        if ((!prevSharingPeer && currentSharingPeer) || (prevSharingPeer && !currentSharingPeer) ||
            (prevSharingPeer && currentSharingPeer && prevSharingPeer.peerID !== currentSharingPeer.peerID)) {

            console.log('Guest TypeSwitch: Screen sharing state changed');
            console.log('Previous sharing peer:', prevSharingPeer ? prevSharingPeer.peerID : 'none');
            console.log('Current sharing peer:', currentSharingPeer ? currentSharingPeer.peerID : 'none');

            // Clear all tracking when screen sharing state changes
            this.attachedScreenVideos.clear();

            // Clear any existing screen video elements
            const allScreenVideos = document.querySelectorAll('[id$="_SCREEN_VIDEO"]');
            allScreenVideos.forEach(video => {
                if (video.srcObject) {
                    console.log('Clearing screen video element:', video.id);
                    video.srcObject = null;
                }
            });
        }

        // Handle new screen sharing attachment
        if (currentSharingPeer && !this.attachedScreenVideos.has(currentSharingPeer.peerID)) {
            console.log('Guest TypeSwitch: Attaching new screen sharing for peer:', currentSharingPeer.peerID);

            // Only attach if not already attached to prevent fluctuation
            const videoElement = document.getElementById(currentSharingPeer.peerID + "_SCREEN_VIDEO");
            if (videoElement && !videoElement.srcObject) {
                const sourceVideo = document.getElementById(currentSharingPeer.peerID + "VIDEO");
                if (sourceVideo && sourceVideo.srcObject) {
                    console.log('Guest TypeSwitch: Attaching stream in componentDidUpdate');
                    videoElement.srcObject = sourceVideo.srcObject;
                    this.attachedScreenVideos.add(currentSharingPeer.peerID);
                } else {
                    console.log('Guest TypeSwitch: Source video not ready in componentDidUpdate');
                }
            } else {
                console.log('Guest TypeSwitch: Video element not ready or already has stream');
            }
        }
    }

    componentWillUnmount() {
        // Clean up intervals to prevent memory leaks
        if (this.job) clearInterval(this.job);
        if (this.intervalId) clearInterval(this.intervalId);
    }

    fetchNestedConfigData = () => {
        // Fetch nested config data for Lark or Pixel Streaming details
        Firebase.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data").onSnapshot({includeMetadataChanges: true}, (doc) => {
            if (doc.exists) {
                const config_data = doc.data();
                this.setState({
                    real_time_config: config_data
                });
                console.log("real_time_config", config_data);
            } else {
                console.log("No nested config document!");
            }
        });
    }

    fetchConfigData = () => {
        // Fetch session configuration data from Firestore
        Firebase.firestore().collection('sessions').doc(this.props.roomId).get()
            .then((doc) => {
                if (doc.exists) {
                    const configData = doc.data();
                    console.log("Setting config", configData);
                    this.props.SetConfig(configData);
                    this.initializeSessionState(configData);
                } else {
                    console.log("No such document!");
                }
            })
            .catch((error) => {
                console.log("Error getting document:", error);
            });
    }

    setupSessionListener = () => {
        // Set up real-time listener for session updates
        Firebase.firestore().collection('sessions').doc(this.props.roomId).onSnapshot({includeMetadataChanges: true}, (doc) => {
            if (!doc.exists) {
                window.location = `/salestool/feedback/${this.props.roomId}`;
                return;
            }
            if (doc.data().status === "ended") {
                window.location = `/salestool/feedback/${this.props.roomId}/${this.props.ClientData.GuestId}`;
                return;
            }
            const configData = doc.data();
            this.props.SetConfig(configData);
            console.log("Setting config", configData);
            this.initializeSessionState(configData);
        });
    }

    initializeSessionState = (config) => {
        // Set initial session state based on config data
        const isSessionStarted = this.CheckStartTiming(config.schedule_time, config.end_time);
        const isSessionEnded = this.checkEndTimimg(config.schedule_time, config.end_time);
        this.setState({
            isSessionStarted: isSessionStarted,
            isSessionEnded: isSessionEnded
        });
    }

    checkNearingEnd = (endTimestamp) => {
        const endTime = new Date(endTimestamp);
        const currentTime = new Date();
        const timeDifference = endTime.getTime() - currentTime.getTime();
        const fiveMinutesInMs = 5 * 60 * 1000;
        return timeDifference <= fiveMinutesInMs && timeDifference > 0;
    }

    setupIntervals = () => {
        this.job = setInterval(() => {
            this.SetDuration(this.state.interval);
            this.props.SetLastUpdate((new Date().getTime()));
        }, this.state.interval * 1000);

        // Changed interval from 500 to 1000ms for more efficient checking
        this.intervalId = setInterval(() => {
            if (this.props.Config) {
                const currentTime = new Date().getTime();
                const startTime = new Date(this.props.Config.schedule_time || this.props.Config.start).getTime();
                const endTime = new Date(this.props.Config.end_time).getTime();

                // console.log("Time Check:", {
                //     current: new Date(currentTime).toISOString(),
                //     start: new Date(startTime).toISOString(),
                //     end: new Date(endTime).toISOString(),
                //     isScheduled: !!this.props.Config.schedule_time,
                //     hasStarted: currentTime >= startTime,
                //     hasEnded: currentTime >= endTime
                // });

                const isSessionStarted = currentTime >= startTime && currentTime < endTime;
                const isSessionEnded = currentTime >= endTime;

                this.setState({
                    isSessionStarted,
                    isSessionEnded,
                    isNearingEnd: this.checkNearingEnd(this.props.Config.end_time),
                    currentTime: new Date()
                });

                if (isSessionStarted && isSessionEnded) {
                    clearInterval(this.intervalId);
                }
            }
        }, 1000); // Increased from 500 to 1000ms
    }

    SetDuration(duration) {
        // Update session duration on the server --mayank

        // First, update the lead duration
        PostRequestWithHeaders({
            url: process.env.REACT_APP_API_BACKEND_API_URL + 'lead/UpdateDuration',
            body: {
                "session_id": this.props.roomId,
                "duration_minutes": duration / 60,
                "lead_id": this.props.ClientData.data._id
            }
        }).then(response => {
            console.log("Lead duration update response:", response);
        });

        // Then, update the session with the correct type
        if (this.props.Config) {
            // Get the current session type and project ID
            const sessionType = this.props.Config?.type || 'default';
            const projectId = this.props.Config?.project_id || null;

            // Determine if pixel streaming is active based on session type
            const isPixelActive = sessionType === 'pixel_streaming' || sessionType === 'lark';

            // Call the UpdateSession API with duration_minutes and lead_id
            UpdateSessionApi(
                this.props.roomId,
                projectId,
                sessionType,
                isPixelActive,
                duration / 60, // Convert duration to minutes
                this.props.ClientData.data._id // Pass the lead_id
            ).then(response => {
                console.log("Session update response:", response);
            }).catch(error => {
                console.error("Error updating session:", error);
            });
        }
    }

    togglePin() {
        // Use parent component's toggle function
        if (this.props.onTogglePin) {
            this.props.onTogglePin();
        }

        // Clear attached videos set to force reattachment when switching
        this.attachedScreenVideos.clear();
    }

    toHumanReadableTime(isoString) {
        // Convert ISO string to human-readable date and time format
        const date = new Date(isoString);
        const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;
        return formattedDate;
    }

    CheckStartTiming(timestamp, endTimestamp) {
        const currentTime = new Date();
        const startTime = new Date(this.props.Config.schedule_time || this.props.Config.start);
        const endTime = new Date(endTimestamp);
        
        console.log("Start Check:", {
            startTime: startTime.toISOString(),
            currentTime: currentTime.toISOString(),
            endTime: endTime.toISOString(),
            isScheduled: !!this.props.Config.schedule_time
        });

        return startTime <= currentTime && currentTime < endTime;
    }

    checkEndTimimg(timestamp, endTimestamp) {
        const currentTime = new Date();
        const endTime = new Date(endTimestamp);
        
        console.log("End Check:", {
            current: currentTime.toISOString(),
            end: endTime.toISOString(),
            hasEnded: currentTime >= endTime
        });

        if (currentTime >= endTime) {
            this.setState({ 
                isSessionStarted: false,
                isSessionEnded: true
            });
            return true;
        }
        return false;
    }



    render() {
        if (!this.props.Config) {
            return <div>Loading...</div>;
        }
        

        // Check if project ID is undefined or type is 'default'
        const isDefaultOrNoProject = !this.props.Config?.project_id ||
                                    this.props.Config?.type === 'default';

        // Content Priority System: Handle overlapping shared content
        // Priority: Screen sharing > Project sharing

        // Check for screen sharing first (highest priority)
        const sharingPeer = this.props.Peers && Object.values(this.props.Peers).find(peer =>
            peer && peer.peerID && this.props.userVideoAudio && this.props.userVideoAudio[peer.peerID] &&
            this.props.userVideoAudio[peer.peerID].screen
        );

        // Check if both screen sharing and project are active
        const hasProject = !isDefaultOrNoProject;
        const hasScreenSharing = !!sharingPeer;
        const showPinIcon = hasProject && hasScreenSharing;
        const isScreenSharingPinned = this.props.isScreenSharingPinned || false;

        // Determine what content to show based on pin state
        // When both are active, show based on pinned content
        // When pin is not active or only one content type exists, use original logic
        let showScreenSharing = hasScreenSharing;
        let showProject = hasProject;

        if (showPinIcon) {
            // When both are active, show based on pinned content
            // Default: Project content in main area, screen sharing in participants area
            // When pinned: Screen sharing in main area, project content hidden
            showScreenSharing = isScreenSharingPinned; // Show screen sharing in main area only when pinned
            showProject = !isScreenSharingPinned; // Show project in main area only when screen sharing is not pinned
        }

        // If screen sharing should be shown
        if (showScreenSharing && sharingPeer) {
            // Determine if this is the host or a guest
            const isHost = sharingPeer.extra && sharingPeer.extra.type === 'host';
            const sharingUserName = isHost ? 'Host' : sharingPeer.userName;

            return (
                <div className="w-full h-full relative bg-black">
                    {/* Pin icon - show when both project and screen sharing are active */}
                    {showPinIcon && (
                        <button
                            onClick={this.togglePin}
                            className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                            title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                        >
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                            </svg>
                        </button>
                    )}

                    {/* Screen sharing content */}
                    <div className="absolute inset-0">
                        <video
                            key={`screen-video-${sharingPeer.peerID}-${isScreenSharingPinned}`}
                            className="w-full h-full object-contain bg-black ScreenSharingInTypeSwitch"
                            id={sharingPeer.peerID + "_SCREEN_VIDEO"}
                            autoPlay
                            playsInline
                            style={{ width: '100%', height: '100%', zIndex: 1 }}
                            ref={(video) => {
                                if (video && !video.srcObject) {
                                    // Check if host has video ON during screen sharing
                                    const hostVideoState = this.props.userVideoAudio[sharingPeer.peerID];
                                    const isHostVideoOn = hostVideoState && hostVideoState.video;

                                    const attachStream = () => {
                                        const sourceVideo = document.getElementById(sharingPeer.peerID + "VIDEO");
                                        if (sourceVideo && sourceVideo.srcObject) {
                                            console.log('Attaching stream to screen video element');
                                            video.srcObject = sourceVideo.srcObject;
                                            this.attachedScreenVideos.add(sharingPeer.peerID);
                                            return true;
                                        }
                                        return false;
                                    };

                                    if (isHostVideoOn) {
                                        // Host video is ON during screen sharing
                                        // The peer connection now contains camera feed for participant video
                                        // We need to use the stored screen sharing stream for main display
                                        // For now, we'll create a separate mechanism to get screen content
                                        console.log('Host video is ON during screen sharing - participant video shows camera, main screen needs screen content');
                                        attachStream();
                                    } else {
                                        // Host video is OFF during screen sharing
                                        // The peer connection contains screen content, use it for main display
                                        console.log('Host video is OFF during screen sharing - using screen content from peer');
                                        if (!attachStream()) {
                                            // Single retry after a short delay if stream not available immediately
                                            setTimeout(() => {
                                                if (!video.srcObject) {
                                                    attachStream();
                                                }
                                            }, 1000);
                                        }
                                    }
                                }
                            }}
                        />
                        {/* User name overlay */}
                        <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded">
                            {sharingUserName} is sharing their screen
                        </div>
                    </div>
                </div>
            );
        }

        // If no screen sharing and project ID is undefined or type is 'default', show default video call interface
        if (isDefaultOrNoProject) {

            return (
                <div className="w-full h-full">
                    {/* The VideoCard components in SideBar will handle screen sharing display automatically */}
                </div>
            );
        }

        // Otherwise, render based on the project type
        if (showProject) {
            switch (this.props.Config.type) {
                case "pixel_streaming": // Handle regular pixel streaming type
                    return (
                        <div className="w-full h-full relative">
                            {/* Pin icon - show when both project and screen sharing are active */}
                            {showPinIcon && (
                                <button
                                    onClick={this.togglePin}
                                    className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                                    title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                                >
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                                    </svg>
                                </button>
                            )}

                            {this.state.isSessionStarted ? (
                                <PixelStreaming SendCustomMessage={this.props.SendCustomMessage} SetMessage={this.props.SetMessage} Config={this.props.Config} roomId={this.props.roomId} SubscribeToCustom={this.props.SubscribeToCustom} />
                            ) : (
                            <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-30 flex items-center justify-center">
                                <div className="bg-[black]/40 bg-opacity-40 backdrop-blur p-6 rounded-lg shadow-md text-center">
                                    <h2 className="text-lg font-bold text-white mb-3">
                                        {this.state.isSessionEnded ? "Session is Over" : "Session Not Started Yet"}
                                    </h2>
                                    <p className="text-base text-white mb-3">
                                        {this.state.isSessionEnded
                                            ? "You're welcome to stay on the call, though the full experience may not be available at this time."
                                            : "The session has not begun. Please wait until the scheduled start time."}
                                    </p>
                                    {!this.state.isSessionEnded && (
                                        <p className="text-md text-white mb-3">
                                            Scheduled Start Time: {this.props.Config?.schedule_time && this.toHumanReadableTime(this.props.Config.schedule_time)}
                                        </p>
                                    )}
                                    <div className="flex justify-end"></div>
                                </div>
                            </div>
                        )}
                        </div>
                    );
                case "lark": // Handle LarkXR type
                    return (
                        <div className="w-full h-full relative">
                            {/* Pin icon - show when both project and screen sharing are active */}
                            {showPinIcon && (
                                <button
                                    onClick={this.togglePin}
                                    className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                                    title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                                >
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                                    </svg>
                                </button>
                            )}

                            {this.state.isSessionStarted ? (
                                <Lark
                                    SendCustomMessage={this.props.SendCustomMessage}
                                    SetMessage={this.props.SetMessage}
                                    Config={this.props.Config}
                                    roomId={this.props.roomId}  // Make sure this is being passed
                                    SubscribeToCustom={this.props.SubscribeToCustom}
                                    isNearingEnd={this.state.isNearingEnd}
                                />
                            ) : (
                                <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-30 flex items-center justify-center">
                                    <div className="bg-[black]/40 bg-opacity-40 backdrop-blur p-6 rounded-lg shadow-md text-center">
                                        <h2 className="text-lg font-bold text-white mb-3">
                                            {this.state.isSessionEnded ? "Session is Over" : "Session Not Started Yet"}
                                        </h2>
                                        <p className="text-base text-white mb-3">
                                            {this.state.isSessionEnded
                                                ? "You're welcome to stay on the call, though the full experience may not be available at this time."
                                                : "The session has not begun. Please wait until the scheduled start time."}
                                        </p>
                                        {!this.state.isSessionEnded && (
                                            <p className="text-md text-white mb-3">
                                                Scheduled Start Time: {this.props.Config?.schedule_time && this.toHumanReadableTime(this.props.Config.schedule_time)}
                                            </p>
                                        )}
                                        <div className="flex justify-end"></div>
                                    </div>
                                </div>
                            )}
                        </div>
                    );
                case "ale":
                    return (
                        <div className="w-full h-full relative">
                            {/* Pin icon - show when both project and screen sharing are active */}
                            {showPinIcon && (
                                <button
                                    onClick={this.togglePin}
                                    className="absolute top-4 right-4 z-[9999990] bg-black/50 hover:bg-black/70 text-white p-2 rounded-lg transition-colors"
                                    title={`${isScreenSharingPinned ? 'Unpin screen sharing to participants' : 'Pin screen sharing to main area'}`}
                                >
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M16,12V4H17V2H7V4H8V12L6,14V16H11.2V22H12.8V16H18V14L16,12Z" />
                                    </svg>
                                </button>
                            )}

                            <AleGuest roomId={this.props.roomId} SubscribeToCustom={this.props.SubscribeToCustom} />
                        </div>
                    );
                default:
                    return <></>;
            }
        }

        // If no content should be shown, return empty
        return <></>;
    }
}

const mapStateTothisprops = state => {
    return {
        Config: state.Call.config,
        ClientData: state.Call.ClientData,
        SessionDetails: state.Sessions.sessions,
        Peers: state.Call.peers, // Fixed: lowercase 'peers' to match Redux state
        userVideoAudio: state.Call.userVideoAudio,

    }
}

const mapDispatchTothisprops = {
    ...HostActions,
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(TypeSwitch)
