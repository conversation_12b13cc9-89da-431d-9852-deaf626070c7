import React from "react";
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction"
import VideoStream from "../../../components/VideoStream";
import { Resolvepath } from "../../../components/Resolvepath"
import './JoiningPageHost.css'
import { getCookie } from "../../../Tools/helpers/domhelper";



class JoiningPageHost extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      video: true,
      audio: true,
      info_details: false,
      bed: '',
      bath: '',
      sqft: '',
      img: '',
      access: false,
      selectDeviceOpen: '',
      projectName: '',
      cameras: [],
      microphones: [],
      speakers: [],
      selectedCamera: '',
      selectedMicrophone: '',
      selectedSpeaker: '',
      isLandscape: window.innerWidth > window.innerHeight,
    }




    this.granted = this.granted.bind(this);
    this.handleJoinClick = this.handleJoinClick.bind(this);
  }

  granted() {
    this.props.SetLocalStream(this.props.DummyAudio);
    this.setState({ access: true })
  }

  handleJoinClick = () => {
    console.log("Join button clicked");
    console.log("LocalStream status:", !!this.props.LocalStream);
    console.log("Calling Join action with roomId:", this.props.roomId);
    this.props.Join();
  }

  async getDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      console.log(`output->data`, devices)
      const cameras = devices.filter(device => device.kind === 'videoinput' && device.deviceId !== 'communications');
      const microphones = devices.filter(device => device.kind === 'audioinput' && device.deviceId !== 'communications');
      const speakers = devices.filter(device => device.kind === 'audiooutput');

      this.setState({
        cameras,
        microphones,
        speakers
      });
      console.log(`output->this.state.cameras`, this.state.cameras)
      console.log(`output->this.state.microphones`, this.state.microphones)
      console.log(`output->this.state.speakers`, this.state.speakers)
    } catch (error) {
      console.log(`output->error.message`, error.message)
    }
  };


  setDeviceOpener(el) {
    this.setState({ selectDeviceOpen: el })
  }
  componentDidMount() {
    //to get available device camera speaker microphone etc
    this.getDevices();

    // Add a resize event listener to track changes in screen dimensions
    window.addEventListener('resize', this.handleOrientationChange);
    const projectName = getCookie('projectName');
    if (projectName) {
      this.setState({ projectName });
    }
  }
  

  componentWillUnmount() {
    // Remove the event listener when the component is unmounted
    window.removeEventListener('resize', this.handleOrientationChange);
  }

  handleOrientationChange = () => {
    const isLandscape = window.innerWidth > window.innerHeight;
    this.setState({ isLandscape });
  };


  render() {
    return (
      <>
        <div className={`${this.state.isLandscape?"block ":"hidden "}`}>
          <div className=" bg-[#171717] w-full h-screen flex justify-center items-center lg:py-">
            <div className="w-1/2">
              <div className="w-11/12 sm:w-4/5 m-auto h-screen flex justify-center items-center">
                <div className="w-full h-fit rounded-xl bg-black">
                  {!this.state.access ? <div className="h-auto w-full pt-[56.25%] relative" >
                    <div className="absolute top-0 left-0 h-full w-full flex justify-center items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="w-6 h-6" viewBox="0 0 24 24" style={{ height: "100%", "position": "relative" }}>
                        <path fill="#FFF" fillRule="evenodd" d="M20 9.4L17.191 12 20 14.6V9.4zm2-.77v6.74c0 .65-.379 1.218-.988 1.484-.224.098-.459.146-.693.146-.206 0-.409-.038-.601-.11L15 12.17v-4.17c0-.553-.448-1-1-1H9.828l-2-2H14c1.654 0 3 1.344 3 3v1.45l2.161-2c.499-.46 1.225-.578 1.851-.306.609.266.988.835.988 1.484zM14 17H5c-.552 0-1-.449-1-1V8c0-.32.161-.593.396-.777L2.974 5.801C2.379 6.351 2 7.13 2 8.001v8c0 1.653 1.346 3 3 3h9c.616 0 1.188-.189 1.665-.508l-1.522-1.523c-.049.008-.092.03-.143.03zm6.707 2.293c.391.39.391 1.023 0 1.414-.195.195-.451.293-.707.293-.256 0-.512-.098-.707-.293L16.386 17.8l-1.455-1.455L5.586 7l-1.76-1.76-.533-.533c-.391-.39-.391-1.414 0-1.414.391-.39 1.023-.39 1.414 0L6.414 5l2 2L15 13.586l2 2 3.707 3.707z" />
                      </svg></div>
                  </div> :
                    <div className="h-fit w-full">

                      {this.props.LocalStream ? <VideoStream /> : ""}
                    </div>}
                </div>
              </div>
            </div>
            <div className="w-1/2 text-white">
              <div className="w-[88%] sm:w-3/5 sm:min-w-[272px] lg:min-w-[380px] min-[1281px]:min-w-[450px] m-auto">
                <p className="text-center mb-3 sm:mb-6 font-bold text-sm sm:text-base sm:text-semibold">You are joining a virtual tour session</p>


                <div className="h-10 sm:h-12 flex  items-center border bg-white rounded sm:w-11/12 lg:w-3/5 m-auto">
                  <div className="h-full">
                    {this.state.img ? <img src={Resolvepath(this.state.img)} alt="avatar" className="h-full rounded-l" /> : <></>}

                  </div>
                  <div className="ml-3">
                    <h4 className="text-black font-semibold text-sm"> {this.state.projectName}</h4>
                  </div>
                </div>



                <div><div><h2 className=" text-xs lg:text-sm  font-semibold text-white  text-center mt-2 mb-2">{!this.state.access ? <>Access to video and voice</> : <>Great, you are about to enter your dream home</>}</h2></div>


                  <div className="text-xs non-italic leading-5 tracking-normal text-center text-[#b7b7b7] px-0 py-0">{!this.state.access ? <>For others to see you and hear you, your browser will request access to your camera and microphone. Click ‘Allow’ to start the camera and mic.</> : <></>}</div>


                </div>




                {!this.state.access ? <div onClick={this.granted} role="button" className="w-full h-12 lg:h-16 sm:h-14 bg-[#1C74D0] rounded-lg mt-3 lg:mt-4 flex justify-center items-center">
                  <svg className="w-6 h-6 " viewBox="0 0 25 25" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_180_22908)">
                      <path d="M14.0757 5.35742H3.12586C1.68164 5.35742 0.5 6.48279 0.5 7.85823V16.286C0.5 17.6614 1.68164 18.7868 3.12586 18.7868H14.0757C15.5199 18.7868 16.7016 17.6614 16.7016 16.286V7.85823C16.7016 6.45778 15.5199 5.35742 14.0757 5.35742Z" fill="white" />
                      <path d="M22.5564 6.73364C22.3989 6.75865 22.2413 6.83367 22.11 6.9087L18.0137 9.15942V14.9613L22.1363 17.212C22.8978 17.6372 23.8431 17.3871 24.2895 16.6619C24.4208 16.4368 24.4996 16.1867 24.4996 15.9116V8.18411C24.4996 7.25881 23.5805 6.50857 22.5564 6.73364Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_180_22908">
                        <rect width="24" height="24" fill="white" transform="translate(0.5 0.0722656)" />
                      </clipPath>
                    </defs>
                  </svg>

                  <div className="text-sm ml-2 font-bold  sm:font-semibold">GRANT ACCESS</div></div>:
                  this.props.LocalStream ? 
                  <div onClick={this.handleJoinClick} role="button" className="w-full h-12  sm:h-14 bg-[#1C74D0] rounded-lg mt-3 lg:mt-4 flex justify-center items-center">
                  <svg className="w-6 h-6 " viewBox="0 0 25 25" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_180_22908)">
                      <path d="M14.0757 5.35742H3.12586C1.68164 5.35742 0.5 6.48279 0.5 7.85823V16.286C0.5 17.6614 1.68164 18.7868 3.12586 18.7868H14.0757C15.5199 18.7868 16.7016 17.6614 16.7016 16.286V7.85823C16.7016 6.45778 15.5199 5.35742 14.0757 5.35742Z" fill="white" />
                      <path d="M22.5564 6.73364C22.3989 6.75865 22.2413 6.83367 22.11 6.9087L18.0137 9.15942V14.9613L22.1363 17.212C22.8978 17.6372 23.8431 17.3871 24.2895 16.6619C24.4208 16.4368 24.4996 16.1867 24.4996 15.9116V8.18411C24.4996 7.25881 23.5805 6.50857 22.5564 6.73364Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_180_22908">
                        <rect width="24" height="24" fill="white" transform="translate(0.5 0.0722656)" />
                      </clipPath>
                    </defs>
                  </svg>

                  <div  className="text-sm ml-2  font-bold sm:font-semibold">START CALL</div>
                   </div> : <div  role="button" className="w-full h-12  sm:h-14 bg-[#1C74D0] rounded-lg mt-3 lg:mt-4 flex justify-center items-center">
                  <svg className="w-6 h-6 " viewBox="0 0 25 25" fill="white" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_180_22908)">
                      <path d="M14.0757 5.35742H3.12586C1.68164 5.35742 0.5 6.48279 0.5 7.85823V16.286C0.5 17.6614 1.68164 18.7868 3.12586 18.7868H14.0757C15.5199 18.7868 16.7016 17.6614 16.7016 16.286V7.85823C16.7016 6.45778 15.5199 5.35742 14.0757 5.35742Z" fill="white" />
                      <path d="M22.5564 6.73364C22.3989 6.75865 22.2413 6.83367 22.11 6.9087L18.0137 9.15942V14.9613L22.1363 17.212C22.8978 17.6372 23.8431 17.3871 24.2895 16.6619C24.4208 16.4368 24.4996 16.1867 24.4996 15.9116V8.18411C24.4996 7.25881 23.5805 6.50857 22.5564 6.73364Z" fill="white" />
                    </g>
                    <defs>
                      <clipPath id="clip0_180_22908">
                        <rect width="24" height="24" fill="white" transform="translate(0.5 0.0722656)" />
                      </clipPath>
                    </defs>
                  </svg>

                  <div  className="text-sm ml-2  font-bold sm:font-semibold">
                  <i id="loginloader" className="fa fa-circle-o-notch fa-spin" style={{ fontSize: "1rem", color: "white", paddingRight: 2, paddingLeft: 2 }}></i>

                  </div>
                   </div>
                   }

                {this.props.NetWorkSpeed?<div className={ this.props.NetWorkSpeed.strength + " w-full h-12 mt-4 hidden sm:block"}>

                  <div style={{ backgroundImage: 'linear-gradient(to right, #FF4444, #FF4444 ,#F98C2C, #FFBB33, #FFBB33,#75C243,#00C851,#75C243)' }} className="w-full h-2 lg:h-3 relative connectionStatusLevel rounded-sm mt-8">
                    <svg className="internetIcon absolute top-[-20px]" width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="8.10059" cy="8" r="8" fill="" />
                    </svg>
                  </div>
                  <div className="connectionStatus text-xs"  > <span >{this.props.NetWorkSpeed.strength.charAt(0).toUpperCase() + this.props.NetWorkSpeed.strength.slice(1).toLowerCase()} Internet Connection</span></div>
                </div>:<></>}
                <div className="py-2 px-1.5 w-fit sm:hidden h-9 rounded bg-[#262626] flex justify-between items-center fixed left-2 bottom-2.5">
                  <div>
                    <svg className="w-6 h-6" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <circle cx="8.10059" cy="8" r="8" className={this.props.NetWorkSpeed.strength == "GOOD" ? "fill-[#00C851]" : this.props.NetWorkSpeed.strength == "POOR" ? "fill-[#FC6C36]" : "fill-[#FFBB33]"} />
                    </svg>
                  </div>
                  <div className="text-xs ml-1 font-semibold">Internet</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={`${this.state.isLandscape?"hidden ":"block "} bg-[#171717] w-full h-screen`}>
          <div className="flex flex-col justify-center items-center w-full h-screen ">
            <h2 className="text-white text-xl mb-3">Please rotate your phone</h2>
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g clip-path="url(#clip0_147_17352)">
                <path d="M72.5 30H50V7.5C50 5.51088 49.2098 3.60322 47.8033 2.1967C46.3968 0.790176 44.4891 0 42.5 0L7.5 0C5.51088 0 3.60322 0.790176 2.1967 2.1967C0.790176 3.60322 0 5.51088 0 7.5L0 72.5C0 74.4891 0.790176 76.3968 2.1967 77.8033C3.60322 79.2098 5.51088 80 7.5 80H72.5C74.4891 80 76.3968 79.2098 77.8033 77.8033C79.2098 76.3968 80 74.4891 80 72.5V37.5C80 35.5109 79.2098 33.6032 77.8033 32.1967C76.3968 30.7902 74.4891 30 72.5 30ZM17.6 5H32.4V6.25H17.6V5ZM45 72.5C45 73.163 44.7366 73.7989 44.2678 74.2678C43.7989 74.7366 43.163 75 42.5 75H7.5C6.83696 75 6.20107 74.7366 5.73223 74.2678C5.26339 73.7989 5 73.163 5 72.5V7.5C5 6.83696 5.26339 6.20107 5.73223 5.73223C6.20107 5.26339 6.83696 5 7.5 5H12.6V8.75C12.6 9.41304 12.8634 10.0489 13.3322 10.5178C13.8011 10.9866 14.437 11.25 15.1 11.25H34.9C35.563 11.25 36.1989 10.9866 36.6678 10.5178C37.1366 10.0489 37.4 9.41304 37.4 8.75V5H42.5C43.163 5 43.7989 5.26339 44.2678 5.73223C44.7366 6.20107 45 6.83696 45 7.5V72.5ZM75 62.4H73.75V47.6H75V62.4ZM75 42.6H71.25C70.587 42.6 69.9511 42.8634 69.4822 43.3322C69.0134 43.8011 68.75 44.437 68.75 45.1V64.9C68.75 65.563 69.0134 66.1989 69.4822 66.6678C69.9511 67.1366 70.587 67.4 71.25 67.4H75V72.5C75 73.163 74.7366 73.7989 74.2678 74.2678C73.7989 74.7366 73.163 75 72.5 75H49.5625C49.8493 74.1976 49.9972 73.3521 50 72.5V35H72.5C73.163 35 73.7989 35.2634 74.2678 35.7322C74.7366 36.2011 75 36.837 75 37.5V42.6Z" fill="white" />
                <path d="M54.0371 9.25072C57.1702 9.23857 60.1937 10.4035 62.5087 12.5148C64.8237 14.626 66.2614 17.5297 66.5371 20.6507L64.7496 19.1132C64.2437 18.7616 63.6257 18.6091 63.0143 18.6851C62.4029 18.7612 61.8411 19.0603 61.4366 19.5251C61.0322 19.99 60.8137 20.5878 60.823 21.2038C60.8324 21.8199 61.0688 22.4108 61.4871 22.8632L67.7371 28.2007C68.19 28.588 68.7663 28.8008 69.3621 28.8007C69.7232 28.8008 70.08 28.7225 70.408 28.5715C70.736 28.4204 71.0274 28.2001 71.2621 27.9257L76.5996 21.6757C76.9513 21.1698 77.1037 20.5518 77.0277 19.9404C76.9517 19.329 76.6525 18.7672 76.1877 18.3628C75.7229 17.9584 75.1251 17.7398 74.509 17.7492C73.8929 17.7585 73.302 17.9949 72.8496 18.4132L71.5996 19.9132C71.143 15.5865 69.091 11.5851 65.844 8.68933C62.5969 5.79357 58.3876 4.21116 54.0371 4.25072C53.3741 4.25072 52.7382 4.51412 52.2693 4.98296C51.8005 5.4518 51.5371 6.08768 51.5371 6.75072C51.5371 7.41377 51.8005 8.04965 52.2693 8.51849C52.7382 8.98733 53.3741 9.25072 54.0371 9.25072Z" fill="white" />
              </g>
              <defs>
                <clipPath id="clip0_147_17352">
                  <rect width="80" height="80" fill="white" />
                </clipPath>
              </defs>
            </svg>
            <h4 className="text-white text-base mt-3">This video is horizontal</h4>
          </div>
        </div>
      </>

    )
  }
}

const mapStateToProps = state => {
  return {
    LocalStream: state.Call.LocalStream,
    NetWorkSpeed: state.Call.NetWorkSpeed,
    DummyAudio: state.Call.DummyAudio,
    Config: state.Call.config,
    ClientData: state.Call.ClientData

  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(JoiningPageHost)