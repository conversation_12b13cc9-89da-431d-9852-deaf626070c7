import React, { Component } from 'react';
import { connect } from 'react-redux';
import * as HostActions from "../../../Actions/HostAction";
import * as ExceptionActions from "../../../Actions/Exception";
import { getScreenSize, addScreenResizeListener } from '../../../utils/screenUtils';
import BottomSheet from '../../../components/common/BottomSheet';

class ChatSidebar extends Component {
  constructor(props) {
    super(props);
    const screenSize = getScreenSize();

    this.state = {
      message: '',
      isMobile: screenSize === 'mobile',
      isTablet: screenSize === 'tablet',
    };
    this.chatContainerRef = React.createRef();
    this.inputFileRef = React.createRef();
  }

  componentDidMount() {
    // Add screen resize listener using our utility
    this.removeResizeListener = addScreenResizeListener((newSize) => {
      // Update all related state at once
      this.setState({
        screenSize: newSize,
        isMobile: newSize === 'mobile',
        isTablet: newSize === 'tablet'
      });
    });

    // Scroll to bottom when component mounts
    this.scrollToBottom();
  }

  componentDidUpdate(prevProps, prevState) {
    // Scroll to bottom when new messages arrive or when chat is opened
    if (prevProps.Messages.length !== this.props.Messages.length ||
      (!prevProps.ChatOpen && this.props.ChatOpen)) {
      this.scrollToBottom();
    }

    // If screen size changed, log it and force re-render
    if (prevState.screenSize !== this.state.screenSize) {
      console.log("Guest ChatSidebar screenSize changed:",
        prevState.screenSize, "->", this.state.screenSize);

      // Force re-render to update UI based on new screen size
      this.forceUpdate();
    }
  }

  componentWillUnmount() {
    // Clean up screen resize listener
    if (this.removeResizeListener) {
      this.removeResizeListener();
    }
  }

  handleScreenSizeChange = (screenSize) => {
    console.log('Guest screen size changed to:', screenSize);
    this.setState({
      isMobile: screenSize === 'mobile',
      isTablet: screenSize === 'tablet'
    }, () => {
      // Force re-render after state update
      this.forceUpdate();
    });
  }

  scrollToBottom = () => {
    setTimeout(() => {
      if (this.chatContainerRef.current) {
        this.chatContainerRef.current.scrollTop = this.chatContainerRef.current.scrollHeight;
      }
    }, 100);
  }

  // Handle message input change
  handleMessageChange = (e) => {
    this.setState({ message: e.target.value });
  }

  // Send message
  sendMessage = (e) => {
    e.preventDefault();

    if (!this.state.message.trim()) {
      return;
    }

    const message = {
      actiontype: "chat",
      room: this.props.roomId,
      message: this.state.message,
      name: this.props.UserName || "",
      type: "message"
    };

    this.props.SendMessage(message, this.props.roomId);
    this.setState({ message: '' });
    this.scrollToBottom();
  }

  // File upload handler
  handleFileUpload = (event) => {
    const validFormats = [".jpg", ".jpeg", ".png", ".pdf", ".docx", ".xlsx", ".pptx", ".txt"];
    let file = event.target.files[0];

    if (!file) return;

    let reader = new FileReader();
    reader.readAsDataURL(file);
    const fileExt = file.name.slice(file.name.lastIndexOf("."));

    if (!validFormats.includes(fileExt)) {
      this.props.CreateToast({ message: "File Format is not supported" });
      return;
    }

    reader.onloadend = () => {
      const message = {
        actiontype: "chat",
        message: "",
        room: this.props.roomId,
        type: "file",
        name: this.props.UserName || "",
        filename: file.name,
        filedata: reader.result
      };
      this.props.SendMessage(message, this.props.roomId);
    };
  }

  // URL formatter for chat messages
  urlify = (text) => {
    var urlRegex = /(https?:\/\/[^\s]+)/g;
    return text.replace(urlRegex, function (url) {
      return '<a class="chat_link" target="_blank" href="' + url + '">' + url + '</a>';
    });
  }

  // Get user initials for avatar
  getInitials = (name) => {
    if (!name) return '?';

    const names = name.split(' ');
    if (names.length === 1) {
      return names[0].charAt(0).toUpperCase();
    } else {
      return (names[0].charAt(0) + names[names.length - 1].charAt(0)).toUpperCase();
    }
  }

  // Format display name (add Host label)
  formatDisplayName = (name) => {
    if (name === 'Host') {
      return 'Host';
    } else if (name.toLowerCase().includes('host')) {
      return name;
    } else if (name === this.props.HostName) {
      return `${name} (Host)`;
    } else if (name === this.props.UserName) {
      return name;
    }
    return name;
  }

  render() {
    // Get current screen size directly to ensure we have the latest
    const screenSize = getScreenSize();
    const isMobile = screenSize === 'mobile';
    const isTablet = screenSize === 'tablet';

    // Update state if needed (without causing re-render loop)
    if (this.state.isMobile !== isMobile || this.state.isTablet !== isTablet) {
      // Use setTimeout to avoid state update during render
      setTimeout(() => {
        this.setState({
          screenSize,
          isMobile,
          isTablet
        });
      }, 0);
    }

    console.log("Guest ChatSidebar render - ChatOpen:", this.props.ChatOpen, "isMobile:", isMobile, "isTablet:", isTablet);

    // For mobile and tablet, use the BottomSheet component
    if ((isMobile || isTablet) && this.props.ChatOpen) {
      return (
        <BottomSheet
          isOpen={true}
          onClose={() => this.props.ToggleChat(false)}
          title="Chats"
          height={isMobile ? '85vh' : '85vh'}
          className="chat-bottom-sheet"
          headerBorder={false}
        >
          <div className="flex flex-col h-full">
            {/* Messages container */}
            <div
              ref={this.chatContainerRef}
              className="flex-1 overflow-y-auto px-3"
            >
              {this.props.Messages.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-32 text-gray-500">
                  <p className="text-sm">No messages yet</p>
                  <p className="text-xs">Send a message to start the conversation</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {this.props.Messages.map((message, index) => {
                    const isConsecutive = index > 0 &&
                      this.props.Messages[index].user === this.props.Messages[index-1].user;
                    const isFromMe = this.props.socketId === message.user;
                    const isHost = message.name === 'Host' || message.name.toLowerCase().includes('host');
                    const avatarBgColor = isHost ? 'bg-purple-600' : (isFromMe ? 'bg-blue-600' : 'bg-gray-600');
                    const messageTime = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

                    if (message.type === "message") {
                      return (
                        <li className={`flex flex-col ${isFromMe ? 'items-end' : 'items-start'} mb-1`} key={index}>
                          <div className={`max-w-[75%]`}>
                            {!isConsecutive && (
                              <div className={`flex items-center ${isFromMe ? 'justify-end' : 'justify-start'} mb-1 px-1`}>
                                {!isFromMe && (
                                  <div className="flex items-center gap-2">
                                    <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                      {this.getInitials(message.name)}
                                    </div>
                                    <div style={{
                                      fontWeight: 600,
                                      fontSize: '14px',
                                      lineHeight: '150%',
                                      letterSpacing: '0%'
                                    }}>
                                      {this.formatDisplayName(message.name)}
                                    </div>
                                    <div className="text-[14px] text-gray-500">
                                      {messageTime}
                                    </div>
                                  </div>
                                )}
                                {isFromMe && (
                                  <div className="flex items-center gap-2">
                                    <div className="text-[14px] text-gray-500">
                                      {messageTime}
                                    </div>
                                    <div style={{
                                      fontWeight: 600,
                                      fontSize: '14px',
                                      lineHeight: '150%',
                                      letterSpacing: '0%'
                                    }}>
                                      {this.formatDisplayName(message.name)}
                                    </div>
                                    <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                      {this.getInitials(message.name)}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                            <div
                              className={`py-2 px-3 rounded-2xl text-[14px] ${isFromMe
                                ? 'bg-gray-100 text-gray-900 rounded-tr-none'
                                : 'bg-gray-100 text-gray-900 rounded-tl-none'}`}
                              dangerouslySetInnerHTML={{ __html: this.urlify(message.message) }}
                            />
                            {isConsecutive && (
                              <div className="text-[14px] text-gray-400 mt-1 px-1 text-right">
                                {messageTime}
                              </div>
                            )}
                          </div>
                        </li>
                      );
                    } else {
                      // File message
                      // Determine avatar color based on user type
                      const isHost = message.name === 'Host' || message.name === this.props.HostName || message.name.toLowerCase().includes('host');
                      const avatarBgColor = isHost ? 'bg-purple-600' : (isFromMe ? 'bg-blue-600' : 'bg-gray-600');
                      const messageTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                      return (
                        <div key={index} className={`flex ${isFromMe ? 'justify-end' : 'justify-start'} mb-2`}>
                          <div className={`max-w-[75%] ${isConsecutive ? 'mt-1' : 'mt-2'}`}>
                            {!isConsecutive && (
                              <div className={`flex items-center mb-1 px-1 ${isFromMe ? 'justify-end' : 'justify-start'}`}>
                                {!isFromMe && (
                                  <div className="flex items-center gap-2">
                                    <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                      {this.getInitials(message.name)}
                                    </div>
                                    <div style={{
                                      fontWeight: 600,
                                      fontSize: '14px',
                                      lineHeight: '150%',
                                      letterSpacing: '0%'
                                    }}>
                                      {this.formatDisplayName(message.name)}
                                    </div>
                                    <div className="text-xs text-gray-400">
                                      {messageTime}
                                    </div>
                                  </div>
                                )}
                                {isFromMe && (
                                  <div className="flex items-center gap-2">
                                    <div className="text-xs text-gray-400">
                                      {messageTime}
                                    </div>
                                    <div style={{
                                      fontWeight: 600,
                                      fontSize: '14px',
                                      lineHeight: '150%',
                                      letterSpacing: '0%'
                                    }}>
                                      {this.formatDisplayName(message.name)}
                                    </div>
                                    <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                      {this.getInitials(message.name)}
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}
                            <div className={`py-2 px-3 rounded-2xl ${isFromMe ? 'bg-gray-100 text-gray-800 rounded-tr-none' : 'bg-gray-100 text-gray-800 rounded-tl-none'}`}>
                              <div className="flex items-center">
                                <span className="text-sm mr-2 truncate">{message.filename}</span>
                                <a
                                  href={message.filedata}
                                  download={message.filename}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="flex-shrink-0"
                                >
                                  <svg className='w-5 h-5' viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M19 16c.55 0 1 .45 1 1v2c0 .51-.388.935-.884.993L19 20H5c-.55 0-1-.45-1-1v-2c0-.55.45-1 1-1s1 .45 1 1v1h12v-1c0-.55.45-1 1-1zM12 3c.553 0 1 .448 1 1v8l2.4-1.8c.442-.333 1.069-.242 1.4.2.332.442.242 1.069-.2 1.4l-4 3c-.177.133-.389.2-.6.2-.201 0-.402-.061-.575-.182l-4-2.814c-.452-.318-.561-.942-.243-1.393.318-.452.941-.561 1.393-.243l2.428 1.71L11 12V4c0-.552.447-1 1-1z" />
                                  </svg>
                                </a>
                              </div>
                              {isConsecutive && (
                                <div className="text-xs text-gray-400 mt-1 px-1 text-left">
                                  {messageTime}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      );
                    }
                  })}
                </div>
              )}
            </div>

            {/* Message input */}
            <div className="px-3 py-1 border-t bg-white">
              <form
                className="flex items-center gap-2.5"
                onSubmit={this.sendMessage}
                style={{ margin: 0, padding: 0 }}
              >
                <button
                  type="button"
                  onClick={() => this.inputFileRef.current.click()}
                  className="flex-shrink-0 p-1 w-6 h-6 text-gray-500 hover:text-gray-700 transition-colors duration-200"
                  disabled={this.state.isUploading}
                >
                  <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6.00033 16.5C4.58627 16.4987 3.23054 15.9926 2.23065 15.0927C1.23076 14.1928 0.668404 12.9726 0.666992 11.7L0.666992 5.3C0.666992 5.08783 0.760643 4.88434 0.927342 4.73431C1.09404 4.58429 1.32013 4.5 1.55588 4.5C1.79163 4.5 2.01772 4.58429 2.18442 4.73431C2.35112 4.88434 2.44477 5.08783 2.44477 5.3L2.44477 11.7C2.44477 12.5487 2.81937 13.3626 3.48617 13.9627C4.15296 14.5629 5.05733 14.9 6.00033 14.9C6.94332 14.9 7.84769 14.5629 8.51448 13.9627C9.18128 13.3626 9.55588 12.5487 9.55588 11.7L9.55588 4.1C9.55588 3.56957 9.32175 3.06086 8.90501 2.68579C8.48826 2.31071 7.92303 2.1 7.33366 2.1C6.74429 2.1 6.17906 2.31071 5.76231 2.68579C5.34556 3.06086 5.11144 3.56957 5.11144 4.1L5.11144 10.9C5.11144 11.1122 5.20509 11.3157 5.37179 11.4657C5.53849 11.6157 5.76458 11.7 6.00033 11.7C6.23607 11.7 6.46217 11.6157 6.62886 11.4657C6.79556 11.3157 6.88921 11.1122 6.88921 10.9V5.3C6.88921 5.08783 6.98286 4.88434 7.14956 4.73431C7.31626 4.58429 7.54236 4.5 7.7781 4.5C8.01385 4.5 8.23994 4.58429 8.40664 4.73431C8.57334 4.88434 8.66699 5.08783 8.66699 5.3V10.9C8.66699 11.5365 8.38604 12.147 7.88594 12.5971C7.38585 13.0471 6.70757 13.3 6.00033 13.3C5.29308 13.3 4.6148 13.0471 4.11471 12.5971C3.61461 12.147 3.33366 11.5365 3.33366 10.9L3.33366 4.1C3.33366 3.14522 3.75509 2.22955 4.50523 1.55442C5.25538 0.879285 6.27279 0.5 7.33366 0.5C8.39452 0.5 9.41194 0.879285 10.1621 1.55442C10.9122 2.22955 11.3337 3.14522 11.3337 4.1L11.3337 11.7C11.3322 12.9726 10.7699 14.1928 9.77 15.0927C8.77011 15.9926 7.41438 16.4987 6.00033 16.5Z" fill="#1C64F2"/>
                  </svg>
                  <input
                    ref={this.inputFileRef}
                    type="file"
                    accept=".jpg, .jpeg, .png, .pdf, .docx, .xlsx, .pptx, .txt"
                    className="hidden"
                    onChange={this.handleFileUpload}
                  />
                </button>

                {/* Text input */}
                <div className="relative flex-1">
                  <input
                    value={this.state.message}
                    onChange={this.handleMessageChange}
                    className="w-full h-[33px] rounded-[8px] border border-gray-300 py-1.5 px-3 outline-none bg-transparent text-sm"
                    placeholder="Type a message..."
                    disabled={this.state.isUploading}
                  />
                </div>

                {/* Send button */}
                <button
                  type="submit"
                  className="flex-shrink-0 w-[32px] h-[32px] flex items-center justify-center rounded-[4px] bg-[#E1EFFE] text-blue-500 hover:text-blue-700 transition-colors duration-200"
                  disabled={!this.state.message.trim() || this.state.isUploading}
                >
                  <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M1.12479 14.7631L15.5246 8.24462C15.6661 8.18065 15.7863 8.07624 15.8707 7.94405C15.9551 7.81186 16 7.65757 16 7.49989C16 7.34221 15.9551 7.18792 15.8707 7.05573C15.7863 6.92354 15.6661 6.81913 15.5246 6.75515L1.12479 0.236684C0.987764 0.17468 0.83639 0.153146 0.687929 0.174538C0.53947 0.195929 0.3999 0.259386 0.28514 0.357669C0.170379 0.455953 0.0850458 0.585107 0.0388746 0.730402C-0.00729752 0.875697 -0.0124493 1.03128 0.0240049 1.17942L1.37518 6.68508L8.79988 6.68508C9.01205 6.68508 9.21553 6.77093 9.36555 6.92373C9.51558 7.07654 9.59986 7.28379 9.59986 7.49989C9.59986 7.71599 9.51558 7.92324 9.36555 8.07605C9.21553 8.22885 9.01205 8.3147 8.79988 8.3147H1.37518L0.0240049 13.8212C-0.0122566 13.9693 -0.00695992 14.1247 0.0392942 14.2699C0.0855474 14.415 0.170899 14.5441 0.285625 14.6422C0.400351 14.7404 0.539839 14.8038 0.688201 14.8251C0.836563 14.8465 0.987837 14.825 1.12479 14.7631Z" fill="#1C64F2"/>
                  </svg>
                </button>
              </form>
            </div>
          </div>
        </BottomSheet>
      );
    }

    // For desktop, render a sidebar - hide on mobile and tablet
    if (!this.props.ChatOpen) {
      return null;
    }

    // If inSidebar is true, we're rendering inside the SideBar component
    const containerClass = this.props.inSidebar
      ? "h-full w-full flex flex-col"
      : `h-full w-[350px] bg-white flex flex-col transition-all duration-300 ease-in-out shadow-lg border-l chat-sidebar ${(isMobile || isTablet) ? 'force-hide-mobile' : ''}`;


    return (
      <div className={containerClass}>
        {/* Chat header - only show if not inside sidebar */}
        {!this.props.inSidebar && (
          <div className="flex items-center justify-between px-3 py-0 border-b bg-white">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold">Chats</h2>
            </div>
            <button
              onClick={() => this.props.ToggleChat(false)}
              className="w-8 h-8 rounded-lg relative cursor-pointer border-[none] transition-all duration-200 flex items-center justify-center hover:bg-gray-100"
            >
              <svg className='w-5 h-5' viewBox="0 0 24 24">
                <defs>
                  <path id="prefix__close" d="M7.414 6l4.293-4.293c.391-.391.391-1.023 0-1.414-.39-.391-1.023-.391-1.414 0L6 4.586 1.707.293C1.317-.098.684-.098.293.293c-.39.391-.39 1.023 0 1.414L4.586 6 .293 10.293c-.39.391-.39 1.023 0 1.414.195.195.451.293.707.293.256 0 .512-.098.707-.293L6 7.414l4.293 4.293c.195.195.451.293.707.293.256 0 .512-.098.707-.293.391-.391.391-1.023 0-1.414L7.414 6z"></path>
                </defs>
                <g fill="currentColor" fillRule="evenodd" transform="translate(6 6)">
                  <use href="#prefix__close"></use>
                </g>
              </svg>
            </button>
          </div>
        )}

        {/* Messages container */}
        <div
          ref={this.chatContainerRef}
         className="flex-1 overflow-y-auto px-3 py-2 bg-white"
        >
          {this.props.Messages.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-gray-500">
              <p className="text-sm">No messages yet</p>
              <p className="text-xs">Send a message to start the conversation</p>
            </div>
          ) : (
            <div className="space-y-1">
              {this.props.Messages.map((message, index) => {
                // Check if this message is from the same user as the previous one
                const isConsecutive = index > 0 && this.props.Messages[index - 1].user === message.user;
                const isFromMe = this.props.socketId === message.user;

                if (message.type === "message") {
                  // Determine avatar color based on user type
                  const isHost = message.name === 'Host' || message.name === this.props.HostName || message.name.toLowerCase().includes('host');
                  const avatarBgColor = isHost ? 'bg-purple-600' : (isFromMe ? 'bg-blue-600' : 'bg-gray-600');
                  const messageTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                  return (
                    <div key={index} className={`flex ${isFromMe ? 'justify-end' : 'justify-start'} mb-2`}>
                      <div className={`max-w-[75%] ${isConsecutive ? 'mt-1' : 'mt-2'}`}>
                        {!isConsecutive && (
                          <div className={`flex items-center mb-1 px-1 ${isFromMe ? 'justify-end' : 'justify-start'}`}>
                            {!isFromMe && (
                              <div className="flex items-center gap-2">
                                <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                  {this.getInitials(message.name)}
                                </div>
                                <div style={{
                                  fontWeight: 600,
                                  fontSize: '14px',
                                  lineHeight: '150%',
                                  letterSpacing: '0%'
                                }}>
                                  {this.formatDisplayName(message.name)}
                                </div>
                                <div className="text-xs text-gray-400">
                                  {messageTime}
                                </div>
                              </div>
                            )}
                            {isFromMe && (
                              <div className="flex items-center gap-2">
                                <div className="text-xs text-gray-400">
                                  {messageTime}
                                </div>
                                <div style={{
                                  fontWeight: 600,
                                  fontSize: '14px',
                                  lineHeight: '150%',
                                  letterSpacing: '0%'
                                }}>
                                  {this.formatDisplayName(message.name)}
                                </div>
                                <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                  {this.getInitials(message.name)}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                        <div className={`py-2 px-3 rounded-2xl ${isFromMe ? 'bg-gray-100 text-gray-800 rounded-tr-none' : 'bg-gray-100 text-gray-800 rounded-tl-none'}`}
                          dangerouslySetInnerHTML={{ __html: this.urlify(message.message) }}
                        />
                        {isConsecutive && (
                          <div className="text-xs text-gray-400 mt-1 px-1 text-left">
                            {messageTime}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                } else {
                  // File message
                  // Determine avatar color based on user type
                  const isHost = message.name === 'Host' || message.name === this.props.HostName || message.name.toLowerCase().includes('host');
                  const avatarBgColor = isHost ? 'bg-purple-600' : (isFromMe ? 'bg-blue-600' : 'bg-gray-600');
                  const messageTime = new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

                  return (
                    <div key={index} className={`flex ${isFromMe ? 'justify-end' : 'justify-start'} mb-2`}>
                      <div className={`max-w-[75%] ${isConsecutive ? 'mt-1' : 'mt-2'}`}>
                        {!isConsecutive && (
                          <div className={`flex items-center mb-1 px-1 ${isFromMe ? 'justify-end' : 'justify-start'}`}>
                            {!isFromMe && (
                              <div className="flex items-center gap-2">
                                <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                  {this.getInitials(message.name)}
                                </div>
                                <div style={{
                                  fontWeight: 600,
                                  fontSize: '14px',
                                  lineHeight: '150%',
                                  letterSpacing: '0%'
                                }}>
                                  {this.formatDisplayName(message.name)}
                                </div>
                                <div className="text-xs text-gray-400">
                                  {messageTime}
                                </div>
                              </div>
                            )}
                            {isFromMe && (
                              <div className="flex items-center gap-2">
                                <div className="text-xs text-gray-400">
                                  {messageTime}
                                </div>
                                <div style={{
                                  fontWeight: 600,
                                  fontSize: '14px',
                                  lineHeight: '150%',
                                  letterSpacing: '0%'
                                }}>
                                  {this.formatDisplayName(message.name)}
                                </div>
                                <div className={`w-5 h-5 rounded-full ${avatarBgColor} text-white flex items-center justify-center text-xs font-bold`}>
                                  {this.getInitials(message.name)}
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                        <div className={`py-2 px-3 rounded-2xl ${isFromMe ? 'bg-gray-100 text-gray-800 rounded-tr-none' : 'bg-gray-100 text-gray-800 rounded-tl-none'}`}>
                          <div className="flex items-center">
                            <span className="text-sm mr-2 truncate">{message.filename}</span>
                            <a
                              target="_blank"
                              rel="noopener noreferrer"
                              href={message.filedata}
                              download={message.filename}
                              className="flex-shrink-0"
                            >
                              <svg className='w-5 h-5' viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 16c.55 0 1 .45 1 1v2c0 .51-.388.935-.884.993L19 20H5c-.55 0-1-.45-1-1v-2c0-.55.45-1 1-1s1 .45 1 1v1h12v-1c0-.55.45-1 1-1zM12 3c.553 0 1 .448 1 1v8l2.4-1.8c.442-.333 1.069-.242 1.4.2.332.442.242 1.069-.2 1.4l-4 3c-.177.133-.389.2-.6.2-.201 0-.402-.061-.575-.182l-4-2.814c-.452-.318-.561-.942-.243-1.393.318-.452.941-.561 1.393-.243l2.428 1.71L11 12V4c0-.552.447-1 1-1z" />
                              </svg>
                            </a>
                          </div>
                          {isConsecutive && (
                            <div className="text-xs text-gray-400 mt-1 px-1 text-left">
                              {messageTime}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                }
              })}
            </div>
          )}
        </div>

        {/* Message input */}
        <div className="px-3 py-1 border-t bg-white">
          <form
            className="flex items-center gap-2.5"
            onSubmit={this.sendMessage}
            style={{ margin: 0, padding: 0 }}
          >
            <button
              type="button"
              onClick={() => this.inputFileRef.current.click()}
              className="flex-shrink-0 p-1 w-6 h-6 text-gray-500 hover:text-gray-700 transition-colors duration-200"
              disabled={this.state.isUploading}
            >
              <svg width="12" height="17" viewBox="0 0 12 17" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6.00033 16.5C4.58627 16.4987 3.23054 15.9926 2.23065 15.0927C1.23076 14.1928 0.668404 12.9726 0.666992 11.7L0.666992 5.3C0.666992 5.08783 0.760643 4.88434 0.927342 4.73431C1.09404 4.58429 1.32013 4.5 1.55588 4.5C1.79163 4.5 2.01772 4.58429 2.18442 4.73431C2.35112 4.88434 2.44477 5.08783 2.44477 5.3L2.44477 11.7C2.44477 12.5487 2.81937 13.3626 3.48617 13.9627C4.15296 14.5629 5.05733 14.9 6.00033 14.9C6.94332 14.9 7.84769 14.5629 8.51448 13.9627C9.18128 13.3626 9.55588 12.5487 9.55588 11.7L9.55588 4.1C9.55588 3.56957 9.32175 3.06086 8.90501 2.68579C8.48826 2.31071 7.92303 2.1 7.33366 2.1C6.74429 2.1 6.17906 2.31071 5.76231 2.68579C5.34556 3.06086 5.11144 3.56957 5.11144 4.1L5.11144 10.9C5.11144 11.1122 5.20509 11.3157 5.37179 11.4657C5.53849 11.6157 5.76458 11.7 6.00033 11.7C6.23607 11.7 6.46217 11.6157 6.62886 11.4657C6.79556 11.3157 6.88921 11.1122 6.88921 10.9V5.3C6.88921 5.08783 6.98286 4.88434 7.14956 4.73431C7.31626 4.58429 7.54236 4.5 7.7781 4.5C8.01385 4.5 8.23994 4.58429 8.40664 4.73431C8.57334 4.88434 8.66699 5.08783 8.66699 5.3V10.9C8.66699 11.5365 8.38604 12.147 7.88594 12.5971C7.38585 13.0471 6.70757 13.3 6.00033 13.3C5.29308 13.3 4.6148 13.0471 4.11471 12.5971C3.61461 12.147 3.33366 11.5365 3.33366 10.9L3.33366 4.1C3.33366 3.14522 3.75509 2.22955 4.50523 1.55442C5.25538 0.879285 6.27279 0.5 7.33366 0.5C8.39452 0.5 9.41194 0.879285 10.1621 1.55442C10.9122 2.22955 11.3337 3.14522 11.3337 4.1L11.3337 11.7C11.3322 12.9726 10.7699 14.1928 9.77 15.0927C8.77011 15.9926 7.41438 16.4987 6.00033 16.5Z" fill="#1C64F2"/>
              </svg>
              <input
                ref={this.inputFileRef}
                type="file"
                accept=".jpg, .jpeg, .png, .pdf, .docx, .xlsx, .pptx, .txt"
                className="hidden"
                onChange={this.handleFileUpload}
              />
            </button>

            {/* Text input */}
            <div className="relative flex-1">
              <input
                value={this.state.message}
                onChange={this.handleMessageChange}
                className="w-full h-[33px] rounded-[8px] border border-gray-300 py-1.5 px-3 outline-none bg-transparent text-sm"
                placeholder="Type a message..."
                disabled={this.state.isUploading}
              />
            </div>

            {/* Send button */}
            <button
              type="submit"
              className="flex-shrink-0 w-[32px] h-[32px] flex items-center justify-center rounded-[4px] bg-[#E1EFFE] text-blue-500 hover:text-blue-700 transition-colors duration-200"
              disabled={!this.state.message.trim() || this.state.isUploading}
            >
              <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.12479 14.7631L15.5246 8.24462C15.6661 8.18065 15.7863 8.07624 15.8707 7.94405C15.9551 7.81186 16 7.65757 16 7.49989C16 7.34221 15.9551 7.18792 15.8707 7.05573C15.7863 6.92354 15.6661 6.81913 15.5246 6.75515L1.12479 0.236684C0.987764 0.17468 0.83639 0.153146 0.687929 0.174538C0.53947 0.195929 0.3999 0.259386 0.28514 0.357669C0.170379 0.455953 0.0850458 0.585107 0.0388746 0.730402C-0.00729752 0.875697 -0.0124493 1.03128 0.0240049 1.17942L1.37518 6.68508L8.79988 6.68508C9.01205 6.68508 9.21553 6.77093 9.36555 6.92373C9.51558 7.07654 9.59986 7.28379 9.59986 7.49989C9.59986 7.71599 9.51558 7.92324 9.36555 8.07605C9.21553 8.22885 9.01205 8.3147 8.79988 8.3147H1.37518L0.0240049 13.8212C-0.0122566 13.9693 -0.00695992 14.1247 0.0392942 14.2699C0.0855474 14.415 0.170899 14.5441 0.285625 14.6422C0.400351 14.7404 0.539839 14.8038 0.688201 14.8251C0.836563 14.8465 0.987837 14.825 1.12479 14.7631Z" fill="#1C64F2"/>
              </svg>
            </button>
          </form>
        </div>
      </div>
    );
  }
}

const mapStateToProps = state => {
  return {
    Messages: state.Call.Messages,
    UnreadCount: state.Call.UnreadCount,
    Tab: state.Call.Tab,
    ChatOpen: state.Call.ChatOpen,
    UserName: state.Call.UserName,
    HostName: state.Call.HostName || 'Host',
  };
};

const mapDispatchToProps = {
  ...HostActions,
  ...ExceptionActions
};

export default connect(mapStateToProps, mapDispatchToProps)(ChatSidebar);