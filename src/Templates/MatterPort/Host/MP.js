import React from  "react"
import { connect } from "react-redux"

class MP extends React.Component{
    constructor(props)
    {
        super(props);
        this.onShowcaseConnect=this.onShowcaseConnect.bind(this);

    }

    async componentDidMount(){
        try {
            const iframe = document.getElementById('showcase-iframe');
             this.mpSdk = await window.MP_SDK.connect(
              iframe, // Obtained earlier
              '73q6cfdu77cnqiw8q2tpfws7d', // Your SDK key
              '' // Unused but needs to be a valid string
            );
            this.onShowcaseConnect();
          } catch (e) {
            console.error(e);
          }
    }
    onShowcaseConnect(){
        this.mpSdk.on(this.mpSdk.Mode.Event.CHANGE_START,
            (oldMode, newMode)=>{
                const data = {
                    actiontype: "Change_Mode",
                    lockmode: true,
                    data: newMode
                  };
                  this.props.SendCustomMessage(data,this.props.roomId)
            }
          );
          var CameraMove;    
          this.mpSdk.on("camera.move",
            (camerapose)=>{
                clearTimeout(CameraMove)
                CameraMove = setTimeout(()=>{ const data = {
                    actiontype: "Change_Rotation",
                    lockmode: true,
                    data: camerapose
                  };
                  this.props.SendCustomMessage(data,this.props.roomId);
                   }, 150);
              
            }
          );
          this.mpSdk.Sweep.current.subscribe( (currentSweep)=> {
            if (currentSweep.sid === '') {
            } else {
                const data = {
                    actiontype: "Change_Sweep",
                    lockmode: true,
                    data: currentSweep
                  };
                  this.props.SendCustomMessage(data,this.props.roomId);
            }
          });
          this.mpSdk.Floor.current.subscribe( (currentFloor) =>{
          
            // Change to the current floor has occurred.
            if (currentFloor.sequence === -1) {
            } else if (currentFloor.sequence === undefined) {
              if (currentFloor.id === undefined) {
              } else {
              }
            } else {
              const data = {
                actiontype: "Change_Floor",
                lockmode: true,
                data: currentFloor.id
              };
              this.props.SendCustomMessage(data,this.props.roomId);
            }
          });
    }
    render(){
        return(
            <>
            <iframe
            style={{width:"100%",height:'100%',position:"absolute"}}
  src={"https://my.matterport.com/show?m="+this.props.pid+"&play=1&applicationKey=73q6cfdu77cnqiw8q2tpfws7d"}
  frameborder="0"
  allow="fullscreen; vr"
  id="showcase-iframe"></iframe>
  </>
        )
    }
}

export default MP;