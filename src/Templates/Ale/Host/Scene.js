import React, { Component } from "react";
import * as Sessions from '../../../Actions/Sessions';
import { connect } from "react-redux";
import Fire from "../../../config/Firebase";
import * as HostActions from '../../../Actions/HostAction'
import { getCookie } from '../../../Tools/helpers/domhelper'
import LoaderOverlay from "../../../components/LoaderOverlay";

class Ale extends Component {
  constructor(props) {
    super(props);
    this.state = {
      updatedLink: '',
      iframeLoaded: false,
      lastRouteUpdate: null
    }
  }

  componentDidMount() {
    console.log("ALE Host - ProjectDetails:", this.props.ProjectDetails);
    console.log("ALE Host - RoomId:", this.props.roomId);

    // Set a timeout to prevent infinite loading
    this.loadingTimeout = setTimeout(() => {
      if (!this.state.updatedLink) {
        console.error("ALE Host - Loading timeout reached, attempting to generate link directly");
        this.generateAleLink();
      }
    }, 10000);

    this.initializeAleLink();
    this.setupMessageHandler();
  }

  componentWillUnmount() {
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }
    if (this.messageHandler) {
      window.removeEventListener('message', this.messageHandler);
    }
  }

  componentDidUpdate(prevProps) {
    const prevProjectId = prevProps.ProjectDetails?._id;
    const currentProjectId = this.props.ProjectDetails?._id;

    console.log("ALE Host - componentDidUpdate:", {
      prevProjectId,
      currentProjectId,
      projectChanged: prevProjectId !== currentProjectId
    });

    if (prevProjectId && currentProjectId && prevProjectId !== currentProjectId) {
      console.log("ALE Host - Project switch detected, reinitializing ALE...");
      this.handleProjectSwitch();
    }
  }

  setupMessageHandler = () => {
    this.messageHandler = (event) => {
      try {
        const data = typeof event.data === 'string' ? JSON.parse(event.data) : event.data;
        
        console.log("ALE Host - Received message:", data);
        
        if (data.actiontype === "routechange") {
          this.UpdateConfig(data.url);
        } else {
          // Forward other messages to guests
          this.props.SendCustomMessage(data, this.props.roomId);
        }
      } catch (error) {
        console.error("ALE Host - Error processing message:", error);
      }
    };

    window.addEventListener('message', this.messageHandler);
  }

  handleProjectSwitch = async () => {
    console.log("ALE Host - Starting project switch sequence...");

    // Reset state
    this.setState({
      updatedLink: '',
      iframeLoaded: false,
      lastRouteUpdate: null
    });

    // Clear existing timeout
    if (this.loadingTimeout) {
      clearTimeout(this.loadingTimeout);
    }

    // Set new timeout
    this.loadingTimeout = setTimeout(() => {
      if (!this.state.updatedLink) {
        console.error("ALE Host - Loading timeout reached during project switch");
        this.generateAleLink();
      }
    }, 10000);

    try {
      // Signal project switch to guests
      const configRef = Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data");
      await configRef.update({
        ale_link: null,
        projectSwitching: true,
        timestamp: new Date().toISOString()
      });

      console.log("ALE Host - Signaled project switch to guests");

      // Wait for Firebase update to propagate
      setTimeout(() => {
        this.initializeAleLink();
      }, 500);

    } catch (error) {
      console.error("ALE Host - Error during project switch:", error);
      this.generateAleLink();
    }
  }

  initializeAleLink = async () => {
    try {
      console.log("ALE Host - Initializing ALE link...");

      if (!this.validateProjectData()) {
        console.error("ALE Host - Invalid project data");
        return;
      }

      const configRef = Fire.firestore().collection('sessions').doc(this.props.roomId).collection("config").doc("data");
      const doc = await configRef.get();

      if (doc.exists) {
        const configData = doc.data();
        if (configData?.ale_link) {
          console.log("ALE Host - Found existing ALE link:", configData.ale_link);
          this.setState({
            updatedLink: configData.ale_link
          });
          this.props.SetConfigData(configData);
          return;
        }
      }

      console.log("ALE Host - Generating new ALE link...");
      await this.generateAndSaveAleLink(configRef);

    } catch (error) {
      console.error("ALE Host - Error initializing ALE link:", error);
      this.generateAleLink();
    }
  }

  validateProjectData = () => {
    const { ProjectDetails } = this.props;

    if (!ProjectDetails?._id || !ProjectDetails?.projectSettings?.ale?.initial_scene_id) {
      console.error("ALE Host - Invalid project data:", {
        hasProjectDetails: !!ProjectDetails,
        projectId: ProjectDetails?._id,
        initialSceneId: ProjectDetails?.projectSettings?.ale?.initial_scene_id
      });
      return false;
    }

    return true;
  }

  generateAleLink = () => {
    if (!this.validateProjectData()) {
      console.error("ALE Host - Cannot generate ALE link");
      return null;
    }

    let organization = getCookie("organization");
    
    if (!organization) {
      organization = this.props.ProjectDetails?.organization_id;
      if (!organization) {
        console.error("ALE Host - No organization found");
        return null;
      }
    }

    // Handle organization parsing
    if (typeof organization === 'string' && organization.startsWith('[')) {
      try {
        const orgArray = JSON.parse(organization);
        organization = orgArray[0];
      } catch (e) {
        console.error("ALE Host - Failed to parse organization:", e);
      }
    }

    const { ProjectDetails } = this.props;
    const sceneType = ProjectDetails.projectSettings.ale?.initial_scene_type === "project"
      ? "projectscene"
      : "masterscene";

    const ale_link = `${process.env.REACT_APP_API_PROPVR_UI_LIBRARY}${organization}/${sceneType}/${ProjectDetails._id}/${ProjectDetails.projectSettings.ale.initial_scene_id}`;

    console.log("ALE Host - Generated ALE link:", ale_link);

    this.setState({
      updatedLink: ale_link
    });

    return ale_link;
  }

  generateAndSaveAleLink = async (configRef) => {
    const ale_link = this.generateAleLink();

    if (!ale_link) {
      console.error("ALE Host - Failed to generate ALE link");
      return;
    }

    try {
      await configRef.set({
        ale_link: ale_link,
        roomId: this.props.roomId,
        projectSwitching: false,
        timestamp: new Date().toISOString()
      });

      const configData = {
        ale_link,
        roomId: this.props.roomId,
        projectSwitching: false
      };
      
      this.props.SetConfigData(configData);
      console.log("ALE Host - Successfully saved ALE link");

    } catch (error) {
      console.error("ALE Host - Error saving ALE link:", error);
    }
  }

  UpdateConfig = async (url) => {
    console.log("ALE Host - Updating config with URL:", url);
    
    // Prevent duplicate updates
    if (this.state.lastRouteUpdate === url) {
      console.log("ALE Host - Duplicate route update, skipping");
      return;
    }

    try {
      // Update Firebase with new URL
      await Fire.firestore()
        .collection("sessions")
        .doc(this.props.roomId)
        .collection("config")
        .doc("data")
        .update({
          ale_link: url,
          projectSwitching: false,
          timestamp: new Date().toISOString()
        });

      // Update local state to track last update
      this.setState({
        lastRouteUpdate: url
      });

      console.log("ALE Host - Successfully updated config");

      // Send route change message to iframe (no src change)
      if (this.state.iframeLoaded) {
        const iframe = document.getElementById('ale-iframe');
        if (iframe?.contentWindow) {
          const routeMessage = {
            actiontype: "routechange",
            url: url,
            simulate: true,
            timestamp: Date.now()
          };
          
          console.log("ALE Host - Sending route change to iframe:", routeMessage);
          iframe.contentWindow.postMessage(routeMessage, '*');
        }
      }

    } catch (error) {
      console.error("ALE Host - Error updating config:", error);
    }
  }

  handleIframeLoad = () => {
    console.log("ALE Host - Iframe loaded successfully");
    this.setState({ iframeLoaded: true });
  }

  handleIframeError = () => {
    console.error("ALE Host - Iframe failed to load");
    this.setState({ iframeLoaded: false });
  }

  render() {
    const { updatedLink } = this.state;
    console.log("ALE Host - Rendering with link:", !!updatedLink);

    return updatedLink ? (
      <iframe
        id="ale-iframe"
        style={{ width: "100%", height: "100%", position: "absolute" }}
        src={updatedLink}
        onLoad={this.handleIframeLoad}
        onError={this.handleIframeError}
      />
    ) : (
      <LoaderOverlay
        title="Preparing Your Virtual Experience"
        message="Just a moment while we set the stage for your immersive journey..."
      />
    );
  }
}

const mapStateToProps = state => {
  return {
    Config: state.Call.config,
    configDetails: state.Sessions.configData,
    ProjectDetails: state.Sessions.projectDetails
  }
}

const mapDispatchToProps = {
  ...Sessions,
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(Ale);