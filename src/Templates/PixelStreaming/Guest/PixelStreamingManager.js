import React from "react";
import { connect } from "react-redux";
import PixelStreaming from "./Pixel";
import MP from "../../LarkXr/Guest/lark";
import Fire from "../../../config/Firebase.jsx";

class PixelStreamingManager extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            projectDetails: null,
            loading: true
        };
    }

    async componentDidMount() {
        // Get the project details from the config
        if (this.props.Config && this.props.Config.project_id) {
            try {
                // Get the project details from Firestore
                const projectRef = await Fire.firestore()
                    .collection('sessions')
                    .doc(this.props.roomId)
                    .get();

                if (projectRef.exists) {
                    const projectData = projectRef.data();
                    this.setState({
                        projectDetails: projectData,
                        loading: false
                    });
                } else {
                    this.setState({ loading: false });
                }
            } catch (error) {
                console.error("Error fetching project details:", error);
                this.setState({ loading: false });
            }
        } else {
            this.setState({ loading: false });
        }
    }

    render() {
        if (this.state.loading) {
            return (
                <div className="fixed left-1/2 transform -translate-x-1/2 top-1/3 z-50 flex items-center justify-center">
                    <div className="bg-black/40 backdrop-blur-md p-8 rounded-xl shadow-lg text-center">
                        <div className="lds-ring mb-4">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                        </div>
                        <h2 className="text-xl font-bold text-white mb-2">
                            Loading Project Details
                        </h2>
                        <p className="text-sm text-gray-300">
                            Please wait while we load the project details...
                        </p>
                    </div>
                </div>
            );
        }
        const isLarkXr = this.props.Config?.pixel_streaming_link &&
                        this.props.Config.pixel_streaming_link.includes('appliId');

        if (isLarkXr) {
            return (
                <MP
                    SendCustomMessage={this.props.SendCustomMessage}
                    SetMessage={this.props.SetMessage}
                    Config={this.props.Config}
                    roomId={this.props.roomId}
                    SubscribeToCustom={this.props.SubscribeToCustom}
                />
            );
        }

        // Otherwise, use the regular PixelStreaming component
        return (
            <PixelStreaming
                SendCustomMessage={this.props.SendCustomMessage}
                SetMessage={this.props.SetMessage}
                Config={this.props.Config}
                roomId={this.props.roomId}
                SubscribeToCustom={this.props.SubscribeToCustom}
            />
        );
    }
}

const mapStateToProps = state => {
    return {};
};

export default connect(mapStateToProps)(PixelStreamingManager);
