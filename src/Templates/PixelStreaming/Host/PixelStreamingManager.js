import React from "react";
import { connect } from "react-redux";
import PixelStreaming from "./Pixel";
import Lark from "../../LarkXr/Host/lark";

class PixelStreamingManager extends React.Component {
    constructor(props) {
        super(props);
    }

    render() {
        console.log("PixelStreamingManager", this.props.ProjectDetails, this.props.SessionDetails);

        // Check if the session type is explicitly set to 'lark'
        const isLarkSession = this.props.SessionDetails?.type === 'lark';

        // Check if the project has LarkXR application_id
        const hasLarkXrAppId = this.props.ProjectDetails?.projectSettings?.pixelstreaming?.application_id;

        // Check if the project has regular pixel streaming endpoint
        const hasPixelStreamingEndpoint = this.props.ProjectDetails?.projectSettings?.pixelstreaming?.pixel_streaming_endpoint;

        // If session type is 'lark' or project has LarkXR application_id, use LarkXR
        if (isLarkSession || hasLarkXrAppId) {
            return (
                <Lark
                    SendCustomMessage={this.props.SendCustomMessage}
                    SetMessage={this.props.SetMessage}
                    roomId={this.props.roomId}
                    isNearingEnd={this.props.isNearingEnd}
                />
            );
        }

        // Otherwise, use regular PixelStreaming
        return (
            <PixelStreaming
                SendCustomMessage={this.props.SendCustomMessage}
                SetMessage={this.props.SetMessage}
                roomId={this.props.roomId}
                isNearingEnd={this.props.isNearingEnd}
            />
        );
    }
}

const mapStateToProps = state => {
    return {
        ProjectDetails: state.Sessions.projectDetails,
        SessionDetails: state.Sessions.sessions,
    };
};

export default connect(mapStateToProps)(PixelStreamingManager);
