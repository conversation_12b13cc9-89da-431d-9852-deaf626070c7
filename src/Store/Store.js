import { combineReducers } from 'redux'
import { createStore, applyMiddleware } from 'redux'
import {thunk} from 'redux-thunk';
import {AuthHandle} from "../Reducers/AuthReducer";
import {Exception} from "../Reducers/Exception";
import {CallHandle} from "../Reducers/HostReducer"
import { HandleSessions } from '../Reducers/Sessions';
const RootReducers = combineReducers({
    Auth: AuthHandle,
    Call:CallHandle,


    Exception:Exception,
    Sessions:HandleSessions
})
const Store = createStore(RootReducers, applyMiddleware(thunk))
export default Store