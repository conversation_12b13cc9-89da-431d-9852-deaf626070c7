import React from 'react'
import {connect} from "react-redux";
import * as HostActions from "../Actions/HostAction"

class VideoStreamNoControls extends React.PureComponent {
  constructor(props){
    super(props);
    this.videoRef = React.createRef()
  }

  componentDidMount () {
    const video = this.videoRef.current

    if ('srcObject' in video) {
      video.srcObject = this.props.LocalStream
    } else {
      video.src = window.URL.createObjectURL(this.props.LocalStream) // for older browsers
    }
  }

  componentDidUpdate (prevProps) {
    const video = this.videoRef.current

    if (video && this.props.LocalStream && prevProps.LocalStream !== this.props.LocalStream) {
      if ('srcObject' in video) {
        video.srcObject = this.props.LocalStream
      } else {
        video.src = window.URL.createObjectURL(this.props.LocalStream)
      }
    }
  }

  render () {
    return (
      <div className='h-fit w-full'>
        <div className='w-full h-auto pt-[56.25%] relative'>
          <video
            className="rounded-[10px] left-0 top-0 absolute w-full h-full object-cover"
            autoPlay
            muted={true}
            ref={this.videoRef}
          />
        </div>
      </div>
    )
  }
}

const mapStateToProps = state => {
  return {
    LocalStream: state.Call.LocalStream,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    NetWorkSpeed: state.Call.NetWorkSpeed,
    DummyAudio: state.Call.DummyAudio
  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(VideoStreamNoControls)
