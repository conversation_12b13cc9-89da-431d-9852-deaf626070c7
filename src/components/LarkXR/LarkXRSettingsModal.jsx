// src/components/LarkXR/LarkXRSettingsModal.jsx
import React, { useState, useEffect } from 'react';
import BottomSheet from '../common/BottomSheet';
import { getScreenSize } from '../../utils/screenUtils';

const LarkXRSettingsModal = ({ larksr, isConnected, onClose }) => {
  const [activeTab, setActiveTab] = useState('Control'); // 'Control' or 'Frame'
  const [screenSize, setScreenSize] = useState(getScreenSize());
  const [settings, setSettings] = useState({
    // Control tab
    mouseMode: 'Auto', // 'Auto' or 'Lock'
    syncOn: true,
    volume: 80,

    // Frame tab
    qualityMode: 'HD', // 'Auto', 'SD', 'HD', 'UHD'
    ratioMode: 'Auto', // 'Auto', 'Zoom', 'HD', 'UHD'
    viewMode: 'Portrait', // 'Portrait', 'Landscape'
    resolutionMode: 'Mobile Small', // 'Mobile Small', 'Mobile Large', 'Tablet', 'Laptop', 'Desktop'
  });

  // Handle screen resize
  useEffect(() => {
    const handleResize = () => {
      setScreenSize(getScreenSize());
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Check if mobile/tablet for bottom sheet
  const isMobileOrTablet = screenSize === 'mobile' || screenSize === 'tablet';

  // Frame tab options
  const qualityOptions = ['Auto', 'SD', 'HD', 'UHD'];
  const ratioOptions = ['Auto', 'Zoom', 'HD', 'UHD'];
  const viewOptions = ['Portrait', 'Landscape'];
  const resolutionOptions = ['Mobile Small', 'Mobile Large', 'Tablet', 'Laptop', 'Desktop'];

  const handleTabClick = (tabName) => {
    setActiveTab(tabName);
  };

  const handleSettingChange = (key, value) => {
    setSettings(prev => ({ ...prev, [key]: value }));

    // Apply setting immediately if connected
    if (isConnected && larksr) {
      try {
        switch (key) {
          case 'customBitrate':
            console.log('Applying bitrate:', value);
            if (larksr.setVideoBitrateKbps) {
              larksr.setVideoBitrateKbps(value);
            }
            break;
          case 'customFps':
            console.log('Applying FPS:', value);
            if (larksr.setVideoFps) {
              larksr.setVideoFps(value);
            }
            break;
          case 'resolution':
            console.log('Applying resolution:', value);
            if (larksr.setCloudAppSize) {
              larksr.setCloudAppSize(value.width, value.height);
            }
            break;
          default:
            console.log('Setting changed:', key, value);
        }
      } catch (error) {
        console.error('Failed to apply setting:', error);
      }
    }
  };

  const handleQualityClick = (qualityType) => {
    setSettings(prev => ({ ...prev, qualityMode: qualityType }));

    const qualitySettings = {
      'Auto': { bitrate: 8000, fps: 60 },
      'SD': { bitrate: 10000, fps: 60 },
      'HD': { bitrate: 20000, fps: 60 },
      'UHD': { bitrate: 50000, fps: 60 }
    };

    const setting = qualitySettings[qualityType];
    if (setting) {
      handleSettingChange('customBitrate', setting.bitrate);
      handleSettingChange('customFps', setting.fps);
    }
  };



  // Render content for both modal and bottom sheet
  const renderContent = () => (
    <>
      {/* Header */}
      <div className="flex items-center justify-between px-4">
        <h2 className="text-lg font-semibold text-gray-900">Settings</h2>
        {!isMobileOrTablet && (
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="px-4 pt-1 border-b border-gray-200">
        <div className="flex space-x-1">
          <button
            onClick={() => handleTabClick('Control')}
            className={`px-0 py-2 text-sm font-medium transition-colors ${
              activeTab === 'Control'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Control
          </button>
          <button
            onClick={() => handleTabClick('Frame')}
            className={`px-4 py-2 text-sm font-medium transition-colors ${
              activeTab === 'Frame'
                ? 'text-blue-600 border-b-2 border-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Frame
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-4 py-2 max-h-[70vh] overflow-y-auto">
        {/* Control Tab */}
        {activeTab === 'Control' && (
          <div className="space-y-6">
            {/* Mouse */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">Mouse</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                <button
                  onClick={() => handleSettingChange('mouseMode', 'Auto')}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    settings.mouseMode === 'Auto'
                      ? 'bg-gray-300 text-gray-700'
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  Auto
                </button>
                <button
                  onClick={() => handleSettingChange('mouseMode', 'Lock')}
                  className={`px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    settings.mouseMode === 'Lock'
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  Lock
                </button>
              </div>
            </div>

            {/* Sync On */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">Sync On</span>
              <button
                onClick={() => handleSettingChange('syncOn', !settings.syncOn)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.syncOn ? 'bg-blue-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.syncOn ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Volume */}
            <div className="flex items-center space-y-2 justify-between">
              <div className=" ">
                <span className="text-sm font-medium text-gray-900">Volume</span>
              </div>
              <div className="flex items-center space-x-1">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M13.2492 0.211553C12.8515 0.0273519 12.4082 -0.0386004 11.9733 0.0217243C11.5384 0.082049 11.1306 0.266049 10.7995 0.551327L4.99254 5.47012H3.12487C2.5171 5.47012 1.93421 5.70877 1.50445 6.13357C1.07469 6.55837 0.833252 7.13453 0.833252 7.73529V12.2656C0.833252 12.8664 1.07469 13.4425 1.50445 13.8673C1.93421 14.2921 2.5171 14.5308 3.12487 14.5308H4.99254L10.8018 19.4507C11.2153 19.805 11.7441 20 12.2913 20C12.6246 19.9996 12.9537 19.9277 13.2561 19.7893C13.6547 19.6096 13.9921 19.3196 14.2275 18.9544C14.463 18.5892 14.5864 18.1645 14.583 17.7314V2.26945C14.5857 1.83571 14.4612 1.41048 14.2245 1.04525C13.9877 0.680015 13.649 0.390432 13.2492 0.211553Z" fill="#6B7280"/>
                  <path d="M17.0006 14.6565C16.7941 14.6564 16.5914 14.6011 16.414 14.4965C16.2366 14.3918 16.0911 14.2417 15.993 14.0621C15.8948 13.8824 15.8476 13.6799 15.8564 13.4759C15.8651 13.2719 15.9295 13.074 16.0427 12.9033C16.5866 12.0305 16.8746 11.0255 16.8746 10.0004C16.8746 8.97537 16.5866 7.97043 16.0427 7.09764C15.8763 6.84623 15.8178 6.53978 15.88 6.24571C15.9422 5.95164 16.1201 5.69404 16.3744 5.52958C16.6288 5.36513 16.9388 5.30728 17.2363 5.36877C17.5338 5.43026 17.7944 5.60605 17.9608 5.85747C18.7619 7.09309 19.1808 8.53293 19.1662 10.0004C19.1811 11.468 18.7622 12.9079 17.9608 14.1434C17.8566 14.3011 17.7142 14.4306 17.5465 14.5202C17.3788 14.6098 17.1912 14.6566 17.0006 14.6565Z" fill="#6B7280"/>
                </svg>
                <div className="flex-1 relative">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={settings.volume}
                    onChange={(e) => handleSettingChange('volume', parseInt(e.target.value))}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    style={{
                      background: `linear-gradient(to right, #3B82F6 0%, #3B82F6 ${settings.volume}%, #E5E7EB ${settings.volume}%, #E5E7EB 100%)`
                    }}
                  />
                </div>
                <svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M10.8356 0.169242C10.4885 0.0218815 10.1017 -0.0308803 9.72209 0.0173794C9.34252 0.0656392 8.98661 0.212839 8.6977 0.441062L3.62987 4.3761H1.99993C1.46952 4.3761 0.960825 4.56702 0.585766 4.90686C0.210706 5.2467 0 5.70762 0 6.18823V9.81249C0 10.2931 0.210706 10.754 0.585766 11.0939C0.960825 11.4337 1.46952 11.6246 1.99993 11.6246H3.62987L8.6997 15.5606C9.06056 15.844 9.5221 16 9.99965 16C10.2905 15.9996 10.5777 15.9421 10.8416 15.8315C11.1895 15.6877 11.4839 15.4557 11.6894 15.1635C11.8949 14.8714 12.0026 14.5316 11.9996 14.1852V1.81556C12.002 1.46857 11.8933 1.12839 11.6867 0.836199C11.4801 0.544012 11.1845 0.312345 10.8356 0.169242Z" fill="#6B7280"/>
                  <path d="M14.1095 11.7252C13.9292 11.7251 13.7523 11.6809 13.5975 11.5972C13.4427 11.5135 13.3158 11.3934 13.2301 11.2497C13.1445 11.1059 13.1033 10.9439 13.1109 10.7807C13.1186 10.6175 13.1747 10.4592 13.2735 10.3226C13.7482 9.62437 13.9995 8.82042 13.9995 8.00036C13.9995 7.1803 13.7482 6.37635 13.2735 5.67811C13.1283 5.47698 13.0773 5.23182 13.1316 4.99657C13.1858 4.76131 13.341 4.55523 13.563 4.42367C13.785 4.2921 14.0556 4.24582 14.3152 4.29502C14.5748 4.34421 14.8023 4.48484 14.9475 4.68597C15.6466 5.67447 16.0122 6.82635 15.9994 8.00036C16.0124 9.1744 15.6468 10.3263 14.9475 11.3147C14.8565 11.4409 14.7322 11.5445 14.5859 11.6162C14.4396 11.6879 14.2758 11.7253 14.1095 11.7252Z" fill="#6B7280"/>
                  <path d="M17.2214 14.544C17.0413 14.5438 16.8646 14.4995 16.7099 14.4158C16.5553 14.3322 16.4284 14.2122 16.3428 14.0687C16.2572 13.9251 16.2159 13.7632 16.2234 13.6001C16.2309 13.4371 16.2869 13.2789 16.3854 13.1423C17.4565 11.604 18.0168 9.81911 17.9994 8.00036C18.0182 6.15666 17.4416 4.34822 16.3404 2.79773C16.1932 2.5978 16.1397 2.35308 16.1917 2.1174C16.2436 1.88172 16.3968 1.67439 16.6174 1.54102C16.8381 1.40766 17.1082 1.35917 17.3683 1.40624C17.6284 1.45331 17.8572 1.59207 18.0044 1.792C19.3238 3.64068 20.0173 5.79884 19.9993 8.00036C20.0248 10.1725 19.3507 12.3042 18.0594 14.1344C17.9683 14.2604 17.8439 14.3638 17.6976 14.4353C17.5513 14.5069 17.3876 14.5442 17.2214 14.544Z" fill="#6B7280"/>
                </svg>
              </div>
            </div>
          </div>
        )}

        {/* Frame Tab */}
        {activeTab === 'Frame' && (
          <div className="space-y-6">
            {/* Quality */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">Quality</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                {qualityOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleQualityClick(option)}
                    className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                      settings.qualityMode === option
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>

            {/* Ratio */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">Ratio</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                {ratioOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleSettingChange('ratioMode', option)}
                    className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                      settings.ratioMode === option
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>

            {/* View */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">View</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                {viewOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleSettingChange('viewMode', option)}
                    className={`px-3 py-1 text-sm font-medium rounded-md transition-colors ${
                      settings.viewMode === option
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>

            {/* Resolution */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-900">Resolution</span>
              <div className="flex bg-gray-100 rounded-lg p-1">
                {resolutionOptions.map((option) => (
                  <button
                    key={option}
                    onClick={() => handleSettingChange('resolutionMode', option)}
                    className={`px-2 py-1 text-xs font-medium rounded-md transition-colors ${
                      settings.resolutionMode === option
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {option}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );

  // Render based on screen size
  if (isMobileOrTablet) {
    return (
      <BottomSheet
        isOpen={true}
        onClose={onClose}
        title="Settings"
        height={screenSize === 'mobile' ? '85vh' : '70vh'}
      >
        {renderContent()}
      </BottomSheet>
    );
  }

  // Desktop modal
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">
      <div className="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-hidden">
        {renderContent()}
      </div>
    </div>
  );
};

export default LarkXRSettingsModal;
