import React from 'react'
import { connect } from "react-redux";
import * as HostActions from "../Actions/HostAction"
import { MicIcon, MicOffIcon, VideoIcon, VideoOffIcon } from '../assets/SvgIcons';
class VideoStream extends React.PureComponent {
  constructor(props) {
    super(props);
    this.videoRef = React.createRef()
    this.HandleVideo = this.HandleVideo.bind(this);
    this.HandleAudio = this.HandleAudio.bind(this);
  }
  componentDidMount() {
    const video = this.videoRef.current

    if ('srcObject' in video) {
      video.srcObject = this.props.LocalStream
    } else {
      video.src = window.URL.createObjectURL(this.props.LocalStream) // for older browsers
    }
  }
  HandleVideo() {
    // Use the same pattern as NewRealTimeController - proper toggle with socket broadcasting
    if (this.props.CameraAccess) {
      this.props.ToggleUserVideo({
        roomId: this.props.roomId,
        Peers: this.props.Peers,
        Audio: this.props.Audio,
        Video: this.props.Video,
        Screen: this.props.ScreenShare,
        LocalStream: this.props.LocalStream
      });
    } else {
      this.props.SetModalException("Camera is not Attached");
    }
  }

  HandleAudio() {
    // Use the same pattern as NewRealTimeController - proper toggle with socket broadcasting
    if (this.props.MicrophoneAccess) {
      this.props.ToggleUserAudio({
        roomId: this.props.roomId,
        Peers: this.props.Peers,
        Audio: this.props.Audio,
        Video: this.props.Video,
        Screen: this.props.ScreenShare,
        LocalStream: this.props.LocalStream
      });
    } else {
      this.props.SetModalException("Mic is not Attached");
    }
  }
  Strength(position, network) {
    switch (network.strength) {
      case "WEEK":
        if (position <= 1) {
          return "red"
        } else {
          return "#ff00006e"
        }
      case "MODERATE":
        if (position <= 2) {
          return "#f57a06"
        } else {
          return "#f57a068f"
        }
      case "BETTER":
        if (position <= 3) {
          return "#3fff08"
        } else {
          return "#3fff08"
        }
      case "EXCELLENT":
        if (position <= 3) {
          return "#3fff08"
        } else {
          return "#3fff08"
        }
      default:
        return "#fff"
    }
  }
  componentDidUpdate() {


  }

  render() {

    return (<>

      <div className='h-fit w-full'>
        <div className='w-full h-auto pt-[56.25%] relative'>
          <video className="rounded-[10px] left-0 top-0 absolute w-full h-full object-none"
            autoPlay muted={true}
            ref={this.videoRef}
          />
          <div
            className='flex flex-row absolute bottom-1 lg:bottom-3 left-1/2 -translate-x-2/4 bg-[transparent] rounded p-2'>
            <div className="mr-3 flex justify-end " >
              <button className={`w-10 h-10 rounded-full ${this.props.Video ? 'bg-white' : 'bg-red-100 text-white'} flex items-center justify-center shadow-lg`} onClick={this.HandleAudio}>
                {this.props.Audio ?
                  <MicIcon />
                  :
                  <MicOffIcon />
                }
              </button>
            </div>
            <div >
              <button onClick={this.HandleVideo} className={`w-10 h-10 rounded-full ${this.props.Audio ? 'bg-white' : 'bg-red-100 text-white'} flex items-center justify-center shadow-lg`}>

                {this.props.Video ?

                  <VideoIcon /> :
                  <VideoOffIcon />
                }


              </button>
            </div>
          </div>
        </div>

      </div>
    </>
    )
  }
}
const mapStateToProps = state => {
  return {
    LocalStream: state.Call.LocalStream,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    NetWorkSpeed: state.Call.NetWorkSpeed,
    DummyAudio: state.Call.DummyAudio,
    // Additional props needed for proper toggle functionality
    Peers: state.Call.peers,
    ScreenShare: state.Call.ScreenShare,
    CameraAccess: state.Call.HasCamera,
    MicrophoneAccess: state.Call.HasMicrophone,
    roomId: state.Call.roomId || window.location.pathname.split('/').pop() // Fallback to URL
  }
}

const mapDispatchToProps = {
  ...HostActions
}

export default connect(mapStateToProps, mapDispatchToProps)(VideoStream)