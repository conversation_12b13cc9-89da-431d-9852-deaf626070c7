import React from 'react';
import { connect } from 'react-redux';

const ScreenShareDebug = ({ userVideoAudio, ScreenShare, Peers }) => {
  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: 'rgba(0,0,0,0.8)',
      color: 'white',
      padding: '10px',
      borderRadius: '5px',
      fontSize: '12px',
      zIndex: 9999,
      maxWidth: '300px'
    }}>
      <h4>Screen Share Debug</h4>
      <div>ScreenShare State: {ScreenShare ? 'TRUE' : 'FALSE'}</div>
      <div>Local User Screen: {userVideoAudio?.localUser?.screen ? 'TRUE' : 'FALSE'}</div>
      
      <h5>Remote Users:</h5>
      {Peers && Object.values(Peers).map(peer => (
        <div key={peer.peerID}>
          {peer.userName || peer.peerID}: {userVideoAudio?.[peer.peerID]?.screen ? 'SHARING' : 'NOT SHARING'}
        </div>
      ))}
      
      <h5>UserVideoAudio State:</h5>
      <pre style={{ fontSize: '10px', maxHeight: '200px', overflow: 'auto' }}>
        {JSON.stringify(userVideoAudio, null, 2)}
      </pre>
    </div>
  );
};

const mapStateToProps = state => ({
  userVideoAudio: state.Call.userVideoAudio,
  ScreenShare: state.Call.ScreenShare,
  Peers: state.Call.peers
});

export default connect(mapStateToProps)(ScreenShareDebug);
