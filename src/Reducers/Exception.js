const Initial_state = {
    stickyexception: false,
    modalexception: false,
    toasts:{}
}

export function Exception(state = Initial_state, action) {
    switch (action.type) {
        case "SET_STICKY_EXCEPTION":
            return { ...state, stickyexception: action.message };
        case "SET_MODAL_EXCEPTION":
            return { ...state, modalexception: action.message };
        case "SET_TOAST_TOAST":
            return {...state, toasts:{...state.toasts,...action.toast}}
        case "DELETE_TOAST":
            var _toasts = state.toasts;
            delete _toasts[action.toastkey];
            return {...state, toasts:{..._toasts}}
        default:
            return state;
    }
}