
export const Initial_state = {
    sessions: {},
    configData: '',
    projectDetails: {},
};


export function HandleSessions(state = Initial_state, action) {
    switch (action.type) {
        case "SET_SESSIONS":
            return {
                ...state,
                sessions: action.payload,
            };

        case "SET_CONFIG_DATA":
            return {
                ...state,
                configData: action.payload,
            };

        case "SET_PROD_DETAILS":
            // console.log(action.payload)
            return {
                ...state,
                projectDetails: action.payload,
            };

        default:
            return state;
    }
}