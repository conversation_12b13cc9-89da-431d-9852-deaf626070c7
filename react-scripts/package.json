{"_args": [["react-scripts@2.1.8", "D:\\Propvr\\SALESTOOL LIVE CURRENT"]], "_from": "react-scripts@2.1.8", "_id": "react-scripts@2.1.8", "_inBundle": false, "_integrity": "sha512-mDC8fYWCyuB9VROti8OCPdHE79UEchVVZmuS/yaIs47VkvZpgZqUvzghYBswZRchqnW0aARNY8xXrzoFRhhK7A==", "_location": "/react-scripts", "_phantomChildren": {"cacache": "11.3.3", "find-cache-dir": "2.1.0", "schema-utils": "1.0.0", "serialize-javascript": "1.6.1", "terser": "3.17.0", "webpack-sources": "1.3.0", "worker-farm": "1.6.0"}, "_requested": {"type": "version", "registry": true, "raw": "react-scripts@2.1.8", "name": "react-scripts", "escapedName": "react-scripts", "rawSpec": "2.1.8", "saveSpec": null, "fetchSpec": "2.1.8"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-scripts/-/react-scripts-2.1.8.tgz", "_spec": "2.1.8", "_where": "D:\\Propvr\\SALESTOOL LIVE CURRENT", "bin": {"react-scripts": "bin/react-scripts.js"}, "browserslist": [">0.2%", "not dead", "not ie <= 11", "not op_mini all"], "bugs": {"url": "https://github.com/facebook/create-react-app/issues"}, "dependencies": {"@babel/core": "7.2.2", "@svgr/webpack": "4.1.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "9.0.0", "babel-jest": "23.6.0", "babel-loader": "8.0.5", "babel-plugin-named-asset-import": "^0.3.1", "babel-preset-react-app": "^7.0.2", "bfj": "6.1.1", "case-sensitive-paths-webpack-plugin": "2.2.0", "css-loader": "1.0.0", "dotenv": "6.0.0", "dotenv-expand": "4.2.0", "eslint": "5.12.0", "eslint-config-react-app": "^3.0.8", "eslint-loader": "2.1.1", "eslint-plugin-flowtype": "2.50.1", "eslint-plugin-import": "2.14.0", "eslint-plugin-jsx-a11y": "6.1.2", "eslint-plugin-react": "7.12.4", "file-loader": "2.0.0", "fs-extra": "7.0.1", "fsevents": "1.2.4", "html-webpack-plugin": "4.0.0-alpha.2", "identity-obj-proxy": "3.0.0", "jest": "23.6.0", "jest-pnp-resolver": "1.0.2", "jest-resolve": "23.6.0", "jest-watch-typeahead": "^0.2.1", "mini-css-extract-plugin": "0.5.0", "optimize-css-assets-webpack-plugin": "5.0.1", "pnp-webpack-plugin": "1.2.1", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "3.0.0", "postcss-preset-env": "6.5.0", "postcss-safe-parser": "4.0.1", "react-app-polyfill": "^0.2.2", "react-dev-utils": "^8.0.0", "resolve": "1.10.0", "sass-loader": "7.1.0", "style-loader": "0.23.1", "terser-webpack-plugin": "1.2.2", "url-loader": "1.1.2", "webpack": "4.28.3", "webpack-dev-server": "3.1.14", "webpack-manifest-plugin": "2.0.4", "workbox-webpack-plugin": "3.6.3"}, "description": "Configuration and scripts for Create React App.", "devDependencies": {"react": "^16.3.2", "react-dom": "^16.3.2"}, "engines": {"node": ">=8.10"}, "files": ["bin", "config", "lib", "scripts", "template", "template-typescript", "utils"], "homepage": "https://github.com/facebook/create-react-app#readme", "license": "MIT", "name": "react-scripts", "optionalDependencies": {"fsevents": "1.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/facebook/create-react-app.git"}, "types": "./lib/react-app.d.ts", "version": "2.1.8"}