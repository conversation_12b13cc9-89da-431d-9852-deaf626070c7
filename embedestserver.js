//server.js
const express = require('express');
const path = require('path');
const port = process.env.REACT_APP_API_PORT || 5002;
const app = express();

// the __dirname is the current directory from where the script is running
app.use(express.static(__dirname));
app.use(express.static(path.join(__dirname, 'salestool')));
app.use(express.static(path.join(__dirname, 'embed')));
app.get('/salestool/pdf', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'pdf/pdf.html'));
});
app.get('/salestool/service-worker.js', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'service-worker.js'));
});
app.get('/salestool/firebase-messaging-sw.js', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'firebase-messaging-sw.js'));
});
app.get('/salestool/embed/*', (req, res) => {
    res.sendFile(path.join(__dirname, 'embed', 'index.html'));
  });
app.get('/salestool/*', (req, res) => {
  res.sendFile(path.join(__dirname, 'salestool', 'index.html'));
});

app.listen(port, () => {
});